import { useState, useRef, useEffect, useTransition } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import {
  Avatar,
  Box,
  Chip,
  ClickAwayListener,
  Divider,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Paper,
  Popper,
  Stack,
  Typography,
  CircularProgress // Added for loading state
} from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import Transitions from '[components]/extended/Transitions';
import { useAuth } from 'Authentication/[contexts]/AuthContext'; // Import useAuth as a hook
import { getRoleDisplayName } from 'RBAC/[services]/roleService'; // Use the service function
import { useRole } from 'RBAC/[contexts]/RoleContext';
import { Role } from 'RBAC/[types]/Role';
import ROUTES from 'Routing/appRoutes';

// assets
import { IconLogout, IconSettings, IconUser } from '@tabler/icons-react';

export default function ProfileSection() {
  const theme = useTheme();
  const navigate = useNavigate();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isPending, startTransition] = useTransition();
  const { userData, isLoading, signOut } = useAuth();
  const { role } = useRole();

  const [open, setOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0); // Keep for list item selection state
  const anchorRef = useRef<any>(null);

  // Removed complex useEffect debug block
  // Removed complex getDisplayName helper function

  const handleLogout = async () => {
    startTransition(() => {
      navigate(ROUTES.AUTH.LOGOUT, { replace: true });
    });
  };

  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const handleListItemClick = (event: React.MouseEvent<HTMLDivElement>, index: number, route: string = '') => {
    setSelectedIndex(index);
    handleClose(event as unknown as MouseEvent | TouchEvent);

    if (route && route !== '') {
      startTransition(() => {
        navigate(route);
      });
    }
  };

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  // Keep Popper focus management
  const prevOpen = useRef(open);
  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current?.focus();
    }
    prevOpen.current = open;
  }, [open]);

  // Display loading indicator until context is initialized
  if (isLoading) {
    return <CircularProgress size={24} sx={{ ml: 2 }} />;
  }

  // Determine user name for display
  const userName = userData?.firstName ? `${userData.firstName} ${userData.lastName || ''}` : userData?.email?.split('@')[0] || 'User';
  
  // Get profile route based on user role
  const getProfileRoute = () => {
    if (!userData?.id || !role) return null;
    
    switch (role) {
      case Role.Patient:
        return ROUTES.PATIENTS.PROFILE(userData.id);
      case Role.Client:
        return ROUTES.CLIENTS.PROFILE(userData.id);
      case Role.Doctor:
        return ROUTES.DOCTORS.PROFILE(userData.id);
      case Role.ClinicAdmin:
        return ROUTES.CLINIC_ADMINS.DETAILS(userData.id);
      case Role.Admin:
        // Admin might not have a specific profile page, could redirect to settings or admin dashboard
        return ROUTES.OTHERS.SETTINGS;
      default:
        return null;
    }
  };

  return (
    <>
      <Chip
        sx={{
          ml: 2,
          height: '48px',
          alignItems: 'center',
          borderRadius: '27px',
          transition: 'all .2s ease-in-out',
          borderColor: theme.palette.mode === 'dark' ? theme.palette.dark.main : theme.palette.primary.light,
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.dark.main : theme.palette.primary.light,
          '&[aria-controls="menu-list-grow"], &:hover': {
            borderColor: theme.palette.primary.main,
            background: `${theme.palette.primary.main}!important`,
            color: theme.palette.primary.light,
            '& svg': {
              stroke: theme.palette.primary.light
            }
          },
          '& .MuiChip-label': {
            lineHeight: 0
          }
        }}
        icon={
          <Avatar
            // src={userData?.avatar} // Use avatar if available in TRQUser
            alt={userName}
            sx={{
              ...theme.typography.mediumAvatar,
              margin: '8px 0 8px 8px !important',
              cursor: 'pointer'
            }}
            ref={anchorRef}
            aria-controls={open ? 'menu-list-grow' : undefined}
            aria-haspopup="true"
            color="inherit"
          >
            {/* Fallback to initials if no avatar src */}
            {/* {userData?.firstName?.charAt(0)}{userData?.lastName?.charAt(0)} */}
          </Avatar>
        }
        label={<IconSettings stroke={1.5} size="24px" />}
        variant="outlined"
        ref={anchorRef}
        aria-controls={open ? 'menu-list-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
        color="primary"
        aria-label="user-account"
      />
      <Popper
        placement="bottom"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 14]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <ClickAwayListener onClickAway={handleClose}>
            <Transitions in={open} {...TransitionProps}>
              <Paper>
                {open && (
                  <MainCard border={false} elevation={16} content={false} boxShadow shadow={'16'}>
                    <Box sx={{ p: 2 }}>
                      {userData ? ( // Check if user object exists
                        <>
                          <Stack>
                            <Stack direction="row" spacing={0.5} alignItems="center">
                              <Typography variant="h4">{userName}</Typography>
                            </Stack>
                            <Typography variant="subtitle2">{userData.email}</Typography>
                            {/* Display Role Information */}
                            <Box sx={{ mt: 1.5, mb: 0.5 }}>
                              <Typography variant="caption" sx={{ fontWeight: 500, color: 'text.secondary', display: 'block', mb: 0.5 }}>
                                Role:
                              </Typography>
                              <Chip
                                size="small"
                                label={getRoleDisplayName(userData.role)} // Directly use service function
                                color="primary"
                                sx={{ height: 20, fontSize: '0.75rem' }}
                              />
                            </Box>
                          </Stack>
                          <Divider sx={{ my: 2 }} />
                          <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: theme.palette.grey[500] } }}>
                            {/* My Profile - show if user has a profile route */}
                            {getProfileRoute() && (
                              <ListItemButton
                                selected={selectedIndex === 0}
                                onClick={(event) => handleListItemClick(event, 0, getProfileRoute() || '')}
                              >
                                <ListItemIcon>
                                  <IconUser stroke={1.5} size="20px" />
                                </ListItemIcon>
                                <ListItemText primary={<Typography variant="body2">My Profile</Typography>} />
                              </ListItemButton>
                            )}
                            <ListItemButton
                              selected={selectedIndex === 1}
                              onClick={(event) => handleListItemClick(event, 1, '/trq/settings')}
                            >
                              <ListItemIcon>
                                <IconSettings stroke={1.5} size="20px" />
                              </ListItemIcon>
                              <ListItemText primary={<Typography variant="body2">Account Settings</Typography>} />
                            </ListItemButton>
                            <Divider sx={{ my: 2 }} />
                            <ListItemButton selected={selectedIndex === 2} onClick={handleLogout}>
                              <ListItemIcon>
                                <IconLogout stroke={1.5} size="20px" />
                              </ListItemIcon>
                              <ListItemText primary={<Typography variant="body2">Logout</Typography>} />
                            </ListItemButton>
                          </List>
                        </>
                      ) : (
                        // Optional: Display something if user is somehow null while Popper is open (shouldn't happen if logic is correct)
                        <Typography>Not logged in</Typography>
                      )}
                    </Box>
                  </MainCard>
                )}
              </Paper>
            </Transitions>
          </ClickAwayListener>
        )}
      </Popper>
    </>
  );
}
