import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Chip,
  Button,
  Card,
  CardContent,
  CardHeader,
  Stack,
  IconButton,
  Tooltip,
  Avatar,
  AppBar,
  Toolbar,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  TextField,
  Alert,
  Switch,
  CardMedia
} from '@mui/material';
import { createProduct } from 'Products/[services]/productsService';
import { syncProductOnTemplatePublish, syncProductOnTemplateUnpublish } from 'Products/[services]/templateProductSyncService';
import { getFirestore } from 'firebase/firestore';
import {
  TextFields as TextIcon,
  CheckBox as CheckboxIcon,
  RadioButtonChecked as BooleanIcon,
  LinearScale as ScaleIcon,
  Event as DateIcon,
  List as SelectIcon,
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ContentCopy as DuplicateIcon,
  Help as HelpIcon,
  Description as DescriptionIcon,
  AccessTime as AccessTimeIcon,
  Category as CategoryIcon
} from '@mui/icons-material';

// project imports
import { getTemplateById, updateTemplate } from 'Questionnaires/Templates/[services]/questionnaireTemplateService';

import MainCard from '[components]/cards/MainCard';
import Breadcrumbs from '[components]/extended/Breadcrumbs';

import { Timestamp } from 'firebase/firestore';
import { Question } from 'Questionnaires/[types]/Question';
import { QuestionnaireStatus } from 'Questionnaires/[types]/Questionnaire';
import { QuestionnaireTemplate } from 'Questionnaires/[types]/QuestionnaireTemplate';
import { assignQuestionnaireToPatient, createQuestionnaire } from 'Questionnaires/[services]/questionnaireService';
import { getUsers } from 'Users/[services]/userService';
import { TRQUser } from 'Users/[types]/User';
import { Role } from 'RBAC/[types]/Role';
import { QuestionnaireWizardProvider } from '../../Wizard/QuestionnaireWizardContext';

// --- Icon mapping utility ---
const QUESTION_TYPE_ICONS = {
  text: TextIcon,
  boolean: BooleanIcon,
  checkbox: CheckboxIcon,
  scale: ScaleIcon,
  date: DateIcon,
  select: SelectIcon
};
function getQuestionIcon(type: string): React.ReactElement {
  const Icon = QUESTION_TYPE_ICONS[type as keyof typeof QUESTION_TYPE_ICONS] || HelpIcon;
  return <Icon />;
}

// --- Main component ---
const ViewTemplate: React.FC = () => {
  const { templateId } = useParams();
  const navigate = useNavigate();
  // const intl = useIntl();
  const [template, setTemplate] = useState<QuestionnaireTemplate | null>(null);
  const [loading, setLoading] = useState(true);
  // const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [publishError, setPublishError] = useState<string | null>(null);
  const [publishSuccess, setPublishSuccess] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [pendingPublishAction, setPendingPublishAction] = useState<'publish' | 'unpublish' | null>(null);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [patients, setPatients] = useState<TRQUser[]>([]);
  const [patientsLoading, setPatientsLoading] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<TRQUser | null>(null);
  const [createAssignError, setCreateAssignError] = useState<string | null>(null);

  // --- Fetch template ---
  useEffect(() => {
    (async () => {
      setLoading(true);
      try {
        const templateData = await getTemplateById(templateId!);
        setTemplate(templateData || null);
      } catch {
        // setError('Failed to load template.');
      } finally {
        setLoading(false);
      }
    })();
  }, [templateId]);

  // --- Fetch patients for assign dialog ---
  useEffect(() => {
    if (!assignDialogOpen) return;
    setPatientsLoading(true);
    getUsers({ role: Role.Patient })
      .then(setPatients)
      .catch(() => setPatients([]))
      .finally(() => setPatientsLoading(false));
  }, [assignDialogOpen]);

  // --- Create and assign questionnaire (generic) ---
  // Removed unused handleCreateAndAssign function
  // --- Create and assign questionnaire to patient ---
  const handleCreateAndAssignToPatient = async () => {
    if (!templateId || !template || !selectedPatient) return;
    setIsCreating(true);
    setCreateAssignError(null);
    try {
      const newQuestionnaireData = {
        title: template.title || template.name,
        name: template.name || '',
        description: template.description || '',
        status: QuestionnaireStatus.Created,
        isReviewed: false,
        templateId,
        patientUid: selectedPatient.uid,
        clientId: selectedPatient.asPatient?.clientId || '',
        hashCode: '',
        isPublished: template.isPublished,
        questions: template.questions,
        version: template.version,
        updatedAt: Timestamp.now(),
        createdAt: Timestamp.now(),
        createdBy: template.createdBy,
        category: template.category,
        tags: template.tags,
        metadata: template.metadata || {}
      };
      const createdQuestionnaire = await createQuestionnaire(newQuestionnaireData as any);
      await assignQuestionnaireToPatient(createdQuestionnaire.id, {
        patientUid: selectedPatient.uid,
        assignedBy: ''
      });
      setAssignDialogOpen(false);
      navigate('/trq/questionnaires', {
        state: {
          newQuestionnaireId: createdQuestionnaire.id,
          openAssignDialog: false,
          newlyCreatedTitle: createdQuestionnaire.name
        }
      });
    } catch (err: any) {
      setCreateAssignError(err?.message || 'Failed to create and assign questionnaire.');
    } finally {
      setIsCreating(false);
    }
  };

  // --- Publish toggle confirmation handlers ---
  const handleConfirmPublishAction = async () => {
    if (!pendingPublishAction) return;

    setIsPublishing(true);
    setPublishError(null);
    setPublishSuccess(false);
    setConfirmDialogOpen(false);

    try {
      if (!templateId) throw new Error('Missing templateId');
      
      if (pendingPublishAction === 'unpublish') {
        // Unpublish template and sync product
        await updateTemplate(templateId, { isPublished: false });
        await syncProductOnTemplateUnpublish(templateId);
        setTemplate({ ...template, isPublished: false });
        setPublishSuccess(false);
      } else {
        // Publish template and sync product
        await updateTemplate(templateId, { isPublished: true });
        await syncProductOnTemplatePublish(templateId);
        setTemplate({ ...template, isPublished: true });
        setPublishSuccess(true);
      }
    } catch (err: any) {
      setPublishError(err?.message || `Failed to ${pendingPublishAction} template.`);
    } finally {
      setIsPublishing(false);
      setPendingPublishAction(null);
    }
  };

  const handleCancelPublishAction = () => {
    setConfirmDialogOpen(false);
    setPendingPublishAction(null);
  };

  // --- Render ---
  if (loading) {
    return (
      <MainCard title="Template Details">
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <Typography>Loading template details...</Typography>
        </Box>
      </MainCard>
    );
  }
  if (!template) {
    return <CircularProgress />;
  }
  return (
    <QuestionnaireWizardProvider questions={template.questions}>
      <MainCard title="Template Details">
        <Breadcrumbs card={false} />
        <AppBar
          position="static"
          color="default"
          elevation={0}
          sx={{ bgcolor: 'background.paper', borderBottom: 1, borderColor: 'divider', mb: 3 }}
        >
          <Toolbar>
            <Button startIcon={<ArrowBackIcon />} onClick={() => navigate('/trq/templates')}>
              Back to Templates
            </Button>
            <Box sx={{ flexGrow: 1 }} />
            <Button
              variant="contained"
              color="primary"
              size="small"
              disabled={isCreating || loading}
              onClick={() => setAssignDialogOpen(true)}
              sx={{ ml: 2 }}
            >
              Create & Assign to Patient
            </Button>
            <Box sx={{ ml: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Publish Toggle and Edit Button */}
              {isPublishing ? (
                <CircularProgress size={28} color="inherit" />
              ) : (
                <>
                  <Tooltip title={template.isPublished ? 'Unpublish Template' : 'Publish Template'}>
                    <Switch
                      checked={!!template.isPublished}
                      onChange={() => {
                        if (template.isPublished) {
                          setPendingPublishAction('unpublish');
                        } else {
                          setPendingPublishAction('publish');
                        }
                        setConfirmDialogOpen(true);
                      }}
                      color="success"
                      inputProps={{ 'aria-label': 'Publish Template Toggle' }}
                      disabled={isPublishing}
                    />
                  </Tooltip>
                  {!template.isPublished && (
                    <Tooltip title="Edit Template">
                      <IconButton onClick={() => navigate(`/trq/templates/${templateId}/edit`)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </>
              )}
              <Tooltip title="Duplicate Template">
                <IconButton>
                  <DuplicateIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete Template">
                <IconButton color="error">
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Toolbar>
        </AppBar>
        {publishError && (
          <Box sx={{ mt: 2 }}>
            <Alert severity="error">{publishError}</Alert>
          </Box>
        )}
        {publishSuccess && (
          <Box sx={{ mt: 2 }}>
            <Alert severity="success">Template published as product successfully.</Alert>
          </Box>
        )}

        {/* Refactored layout: Template Info at top, Questions below */}
        <Box sx={{ mb: 4, mt: 2, px: { xs: 1, sm: 2, md: 3 } }}>
          <Card variant="outlined" sx={{ mb: 3, p: { xs: 2, md: 3 } }}>
            {template.coverImage && (
              <CardMedia
                component="img"
                height="200"
                image={template.coverImage}
                alt={template.name}
                sx={{
                  borderRadius: 1,
                  mb: 2,
                  objectFit: 'cover',
                  objectPosition: 'center'
                }}
              />
            )}
            <CardContent>
              <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} alignItems="flex-start" justifyContent="space-between">
                <Avatar sx={{ bgcolor: 'primary.main', width: 64, height: 64 }}>
                  <DescriptionIcon fontSize="large" />
                </Avatar>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography variant="h4" sx={{ fontWeight: 700, mb: 1, wordBreak: 'break-word' }}>
                    {template.name}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 2, wordBreak: 'break-word' }}>
                    {template.description || <span style={{ color: '#888' }}>No description provided</span>}
                  </Typography>
                  <Stack direction="row" spacing={1.5} sx={{ flexWrap: 'wrap', mb: 2 }}>
                    <Chip
                      label={template.isPublished ? 'Published' : 'Draft'}
                      color={template.isPublished ? 'success' : 'default'}
                      size="medium"
                      sx={{ fontWeight: 600, fontSize: 15 }}
                    />
                    <Chip
                      icon={<TextIcon />}
                      label={`${template.questions.length} ${template.questions.length === 1 ? 'Question' : 'Questions'}`}
                      color="primary"
                      sx={{ fontWeight: 600, fontSize: 15 }}
                    />
                    {template.metadata?.category && (
                      <Chip
                        icon={<CategoryIcon />}
                        label={template.metadata.category}
                        color="secondary"
                        sx={{ fontWeight: 500, fontSize: 15 }}
                      />
                    )}
                    {template.metadata?.estimatedTime && (
                      <Chip
                        icon={<AccessTimeIcon />}
                        label={`${template.metadata.estimatedTime} min`}
                        color="info"
                        sx={{ fontWeight: 500, fontSize: 15 }}
                      />
                    )}
                  </Stack>
                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ flexWrap: 'wrap', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">
                      <b>Created:</b> {template.createdAt ? new Date(template.createdAt).toLocaleString() : '-'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <b>Last Updated:</b> {template.updatedAt ? new Date(template.updatedAt).toLocaleString() : '-'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      <b>Version:</b> {template.version || '1'}
                    </Typography>
                  </Stack>
                  {template.tags && template.tags.length > 0 && (
                    <Stack direction="row" spacing={1} sx={{ flexWrap: 'wrap', mt: 1 }}>
                      {template.tags.map((tag: string) => (
                        <Chip key={tag} label={tag} size="small" color="info" variant="outlined" />
                      ))}
                    </Stack>
                  )}
                </Box>
                <Box sx={{ minWidth: 200, maxWidth: 260, ml: { md: 3 }, mt: { xs: 3, md: 0 } }}>
                  <Stack spacing={1}>
                    <Typography variant="subtitle2" color="text.secondary">
                      <b>Template ID:</b> {templateId}
                    </Typography>
                    {template.createdBy && (
                      <Typography variant="subtitle2" color="text.secondary">
                        <b>Created By:</b> {template.createdBy}
                      </Typography>
                    )}
                  </Stack>
                </Box>
              </Stack>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ px: { xs: 1, sm: 2, md: 3 }, mb: 4 }}>
          <Card variant="outlined">
            <CardHeader
              title={
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  Questions
                </Typography>
              }
              subheader={
                <Typography variant="subtitle2" color="text.secondary">
                  Total questions: {template.questions.length}
                </Typography>
              }
            />
            <CardContent>
              <Stack spacing={2}>
                {template.questions.map((question: Question, index: number) => (
                  <Card key={question.id} variant="outlined" sx={{ bgcolor: 'background.default' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                        <Tooltip title={question.questionType}>{getQuestionIcon(question.questionType)}</Tooltip>
                        <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                          {index + 1}. {question.text}
                        </Typography>
                        {question.isOptional && <Chip label="Optional" size="small" color="info" />}
                      </Box>
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Box>
        {/* Assign to Patient Dialog */}
        <Dialog open={assignDialogOpen} onClose={() => setAssignDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Assign to Patient</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Autocomplete
                options={patients}
                getOptionLabel={(option) => `${option.firstName} ${option.lastName} (${option.email})`}
                value={selectedPatient}
                onChange={(_, value) => setSelectedPatient(value)}
                renderInput={(params) => <TextField {...params} label="Select Patient" fullWidth />}
                loading={patientsLoading}
                disabled={patientsLoading}
              />
              {createAssignError && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {createAssignError}
                </Alert>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setAssignDialogOpen(false)} disabled={isCreating}>
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              disabled={!selectedPatient || isCreating}
              onClick={handleCreateAndAssignToPatient}
              startIcon={isCreating ? <CircularProgress color="inherit" size={20} /> : null}
            >
              {isCreating ? 'Creating...' : 'Create & Assign'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Confirmation Dialog */}
        <Dialog
          open={confirmDialogOpen}
          onClose={handleCancelPublishAction}
          aria-labelledby="publish-confirmation-dialog"
        >
          <DialogTitle id="publish-confirmation-dialog">
            {pendingPublishAction === 'publish' ? 'Publish Template' : 'Unpublish Template'}
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ pt: 1 }}>
              {pendingPublishAction === 'publish' 
                ? 'Are you sure you want to publish this template? Once published, it will be available to users.'
                : 'Are you sure you want to unpublish this template? It will no longer be available to users.'}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelPublishAction} color="inherit">
              Cancel
            </Button>
            <Button 
              onClick={handleConfirmPublishAction} 
              color={pendingPublishAction === 'publish' ? 'success' : 'warning'}
              variant="contained"
              disabled={isPublishing}
            >
              {isPublishing ? 'Processing...' : (pendingPublishAction === 'publish' ? 'Publish' : 'Unpublish')}
            </Button>
          </DialogActions>
        </Dialog>
      </MainCard>
    </QuestionnaireWizardProvider>
  );
};

export default ViewTemplate;
