// Questionnaire Template Service
// Handles operations for questionnaire templates

import { QuestionnaireTemplate } from '../../[types]/QuestionnaireTemplate';
import {
  collection,
  doc,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  getDocs,
  Timestamp,
  orderBy,
  limit
} from 'firebase/firestore';
import { db as firestore } from 'Firebase/[config]/firebase';

// Placeholder type until TemplateVersion.ts is restored or defined
// Omit 'version' as well, as it's required in the base type and likely refers to the main template's current version
interface TemplateVersionData extends Omit<QuestionnaireTemplate, 'id' | 'createdAt' | 'updatedAt' | 'version'> {
  versionId?: string; // ID of the version document itself
  savedAt?: Timestamp; // Keep as Timestamp from Firestore
  versionNumber?: number; // Optional version number specific to this historical record
  // Updated based on QuestionnaireTemplate definition
  name: string; // Required
  isPublished: boolean; // Required
  // version?: number; // Removed, as it's omitted now
  changes?: string; // Added based on VersionHistory error
  // Keep createdAt/updatedAt as Timestamp from Firestore for the version data itself
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

// Collection references
const templatesCollection = collection(firestore, 'questionnaireTemplates');

/**
 * Convert Firestore document to QuestionnaireTemplate
 */
export const convertToTemplate = (doc: any): QuestionnaireTemplate => {
  const data = doc.data();

  return <QuestionnaireTemplate>{
    id: doc.id,
    name: data.name || '',
    title: data.title || '',
    description: data.description || '',
    createdBy: data.createdBy || '',
    isPublished: data.isPublished || false,
    questions: data.questions || [],
    category: data.category || '',
    tags: data.tags || [],
    version: data.version || 1,
    createdAt: data.createdAt || Timestamp.now(),
    updatedAt: data.updatedAt || Timestamp.now(),
    metadata: data.metadata || {},
    coverImage: data.coverImage || undefined,
    versionHistory: data.versionHistory || undefined
  };
};

/**
 * Get a template by ID
 */
export const getTemplateById = async (id: string): Promise<QuestionnaireTemplate | null> => {
  try {
    const templateRef = doc(templatesCollection, id);
    const templateSnap = await getDoc(templateRef);
    if (!templateSnap.exists()) return null;
    return convertToTemplate(templateSnap);
  } catch (error) {
    console.error('Error getting template by ID:', error);
    throw error;
  }
};

/**
 * Get a specific version of a template
 */
export const getTemplateVersion = async (templateId: string, versionId: string): Promise<TemplateVersionData | null> => {
  try {
    const versionDocRef = doc(firestore, `questionnaire_templates/${templateId}/versions/${versionId}`);
    const versionDocSnap = await getDoc(versionDocRef);

    if (!versionDocSnap.exists()) {
      console.warn(`Version ${versionId} for template ${templateId} not found.`);
      return null;
    }

    // Assuming the version document structure is similar to the template,
    // potentially with additional version metadata.
    const data = versionDocSnap.data();
    return {
      versionId: versionDocSnap.id, // Add the version ID itself
      name: data.name || '', // Added missing name property
      title: data.title || '',
      description: data.description || '',
      isPublished: data.isPublished || false, // Added missing isPublished property
      createdBy: data.createdBy || '', // May or may not be relevant in version history
      questions: data.questions || [],
      category: data.category || '',
      tags: data.tags || [],
      metadata: data.metadata || {},
      savedAt: data.savedAt || undefined, // Assuming a 'savedAt' field for versions
      versionNumber: data.versionNumber || undefined // Assuming a 'versionNumber' field
    };
  } catch (error) {
    console.error(`Error getting template version ${versionId} for template ${templateId}:`, error);
    throw error;
  }
};

/**
 * Restore a template to a specific version
 */
export const restoreTemplateVersion = async (templateId: string, versionId: string): Promise<QuestionnaireTemplate | null> => {
  try {
    const versionData = await getTemplateVersion(templateId, versionId);

    if (!versionData) {
      throw new Error(`Version ${versionId} not found for template ${templateId}. Cannot restore.`);
    }

    // Prepare data for updating the main template document
    // Exclude version-specific fields and timestamp fields (updateTemplate handles updatedAt)

    const {
      versionId: _vId,
      savedAt: _sAt,
      versionNumber: _vNum,
      createdAt: _cAt,
      updatedAt: _uAt,
      ...templateDataFromVersion
    } = versionData;

    // Ensure the version type matches if present (it's number in QuestionnaireTemplate)
    const updateData: Partial<QuestionnaireTemplate> = {
      ...templateDataFromVersion
      // version: templateDataFromVersion.version // Removed - version doesn't exist here due to Omit
    };

    // Update the main template document with the data from the specified version
    const updatedTemplate = await updateTemplate(templateId, updateData);

    console.log(`Template ${templateId} restored to version ${versionId}`);
    return updatedTemplate;
  } catch (error) {
    console.error(`Error restoring template ${templateId} to version ${versionId}:`, error);
    throw error; // Re-throw the error for the caller to handle
  }
};

/**
 * Get templates with filtering options
 */
export const getTemplates = async (
  options: {
    clinicId?: string;
    createdBy?: string;
    isActive?: boolean;
    category?: string;
    tags?: string[];
    limit?: number;
    orderByField?: string;
    orderDirection?: 'asc' | 'desc';
  } = {}
): Promise<QuestionnaireTemplate[]> => {
  try {
    let q = query(templatesCollection);

    // Apply filters
    if (options.clinicId) {
      q = query(templatesCollection, where('clinicId', '==', options.clinicId));
    }

    if (options.createdBy) {
      q = query(templatesCollection, where('createdBy', '==', options.createdBy));
    }

    if (options.isActive !== undefined) {
      q = query(templatesCollection, where('isActive', '==', options.isActive));
    }

    if (options.category) {
      q = query(templatesCollection, where('category', '==', options.category));
    }

    // Note: Firestore doesn't support array-contains-any with other where clauses
    // If using tags filter, handle it client-side or restructure the query

    // Apply sorting
    if (options.orderByField) {
      q = query(templatesCollection, orderBy(options.orderByField, options.orderDirection || 'desc'));
    } else {
      q = query(templatesCollection, orderBy('createdAt', 'desc'));
    }

    // Apply limit
    if (options.limit) {
      q = query(templatesCollection, limit(options.limit));
    }

    const querySnapshot = await getDocs(q);
    let templates = querySnapshot.docs.map((doc: any) => convertToTemplate(doc));

    // Apply tags filter client-side if needed
    if (options.tags && options.tags.length > 0) {
      templates = templates.filter((template: QuestionnaireTemplate) => {
        return options.tags!.some((tag) => template.tags.includes(tag));
      });
    }

    return templates;
  } catch (error) {
    console.error('Error getting templates:', error);
    throw error;
  }
};

/**
 * Create a new template
 */
export const createTemplate = async (data: Omit<QuestionnaireTemplate, 'id'>): Promise<QuestionnaireTemplate> => {
  try {
    const now = Timestamp.now();
    const templateData = {
      ...data,
      createdAt: now,
      updatedAt: now
    };

    const docRef = await addDoc(templatesCollection, templateData);
    const newTemplate = await getDoc(docRef);
    return convertToTemplate(newTemplate);
  } catch (error) {
    console.error('Error creating template:', error);
    throw error;
  }
};

/**
 * Update an existing template
 */
export const updateTemplate = async (id: string, data: Partial<QuestionnaireTemplate>): Promise<QuestionnaireTemplate> => {
  try {
    const templateRef = doc(templatesCollection, id);
    const templateDoc = await getDoc(templateRef);

    if (!templateDoc.exists) {
      throw new Error(`Template with ID ${id} not found`);
    }

    const now = Timestamp.now();
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _, ...dataWithoutId } = data;

    await updateDoc(templateRef, {
      ...dataWithoutId,
      updatedAt: now
    });

    const updatedTemplate = await getDoc(templateRef);
    return convertToTemplate(updatedTemplate);
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

/**
 * Delete a template
 */
export const deleteTemplate = async (id: string): Promise<void> => {
  try {
    const templateRef = doc(templatesCollection, id);
    await deleteDoc(templateRef);
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

/**
 * Clone a template
 */
export const cloneTemplate = async (id: string, overrides: Partial<QuestionnaireTemplate> = {}): Promise<QuestionnaireTemplate> => {
  try {
    const sourceTemplate = await getTemplateById(id);

    if (!sourceTemplate) {
      throw new Error(`Template with ID ${id} not found`);
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id: _, ...templateWithoutId } = sourceTemplate;

    return createTemplate({
      ...templateWithoutId,
      title: `${sourceTemplate.title} (Copy)`,
      ...overrides
    });
  } catch (error) {
    console.error('Error cloning template:', error);
    throw error;
  }
};
