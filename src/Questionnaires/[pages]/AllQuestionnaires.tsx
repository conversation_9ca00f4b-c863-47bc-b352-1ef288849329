import React, { useState, useEffect, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import { Timestamp } from 'firebase/firestore';
import { auth } from '../../Firebase/[config]/firebase';

// material-ui
import Stack from '@mui/material/Stack';
import { GridColDef, GridRenderCellParams, GridRowSelectionModel } from '@mui/x-data-grid';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Button from '@mui/material/Button';
import { SelectChangeEvent } from '@mui/material/Select';

// project imports
import MainCard from '../../[components]/cards/MainCard';
import useDataGrid from '../../[hooks]/useDataGrid';
import {
  getQuestionnaires,
  deleteQuestionnaire,
  createQuestionnaire,
  assignQuestionnaireToPatient
} from '../[services]/questionnaireService';
import { getUsers } from '../../Users/<USER>/userService';
import { Questionnaire, QuestionnaireStatus } from 'Questionnaires/[types]/Questionnaire';
import TemplateSelectionDialog from '../Templates/TemplateSelectionDialog';
import AssignQuestionnaireDialog from './AssignQuestionnaireDialog';
import { TRQUser } from '../../Users/<USER>/User';
import usePermission from '../../RBAC/[hooks]/usePermission';
import { useRole } from '../../RBAC/[contexts]/RoleContext';
import QuestionnaireToolbar from '../[components]/QuestionnaireToolbar';
import TrqDataGrid from '../../[components]/trq/TrqDataGrid';
import ROUTES from 'Routing/appRoutes';

// (All icons are now imported in QuestionnaireToolbar)

interface GridValueFormatterParams {
  value: Timestamp | undefined;
}

const formatTimestamp = (timestamp: Timestamp | undefined): string => {
  if (!timestamp) return '';
  return timestamp.toDate().toLocaleDateString();
};

const AllQuestionnaires = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const dataGridStyles = useDataGrid();
  const permissions = usePermission();
  // Use role hook but don't focus on role management in this component
  useRole();
  const [questionnaires, setQuestionnaires] = useState<Questionnaire[]>([]);
  const [users, setUsers] = useState<TRQUser[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);

  // Main data fetching effect
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [questionnairesData, usersData] = await Promise.all([getQuestionnaires(), getUsers()]);
        console.log(
          'Fetched questionnaires data:',
          questionnairesData.slice(0, 3).map((q) => ({ id: q.id, createdAt: q.createdAt }))
        );
        setQuestionnaires(questionnairesData);
        setUsers(usersData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const userMap = useMemo(() => {
    return users.reduce(
      (acc, user) => {
        if (user.uid) {
          acc[user.uid] = `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email || user.uid;
        }
        return acc;
      },
      {} as Record<string, string>
    );
  }, [users]);

  // Handle row selection
  const handleSelectionChange = (rowSelectionModel: GridRowSelectionModel) => {
    setSelectedRows(rowSelectionModel.map((id) => id.toString()));
  };

  // Handle view action
  const handleView = (id: string) => {
    // Simple permission check without dynamic context
    if (permissions.canViewQuestionnaire()) {
      navigate(ROUTES.QUESTIONNAIRES.DETAILS(id));
    }
  };

  // Handle delete action
  const handleDelete = async (id: string) => {
    // Simple permission check without dynamic context
    if (permissions.canDeleteQuestionnaire()) {
      try {
        await deleteQuestionnaire(id);
        setQuestionnaires(questionnaires.filter((q) => q.id !== id));
        setSelectedRows(selectedRows.filter((rowId) => rowId !== id));
      } catch (error) {
        console.error('Error deleting questionnaire:', error);
      }
    }
  };

  // Handle template selection
  const handleTemplateSelect = async (templateId: string) => {
    try {
      const newQuestionnaire = await createQuestionnaire({
        title: `New Questionnaire from Template ${templateId.substring(0, 5)}...`,
        description: 'Created from template',
        status: QuestionnaireStatus.Created,
        isReviewed: false,
        templateId,
        assignedPatient: '',
        clientId: '',
        name: '',
        questions: [],
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        responses: [],
        createdBy: '',
        category: '',
        tags: [],
        template: {
          id: templateId,
          name: '',
          title: '',
          createdBy: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          isPublished: false,
          questions: [],
          version: 1,
          category: '',
          tags: []
        }
      });

      setQuestionnaires([newQuestionnaire, ...questionnaires]);
      setTemplateDialogOpen(false);
    } catch (error) {
      console.error('Error creating questionnaire from template:', error);
    }
  };

  // Handle opening the assign dialog from the toolbar
  const handleOpenAssignDialog = () => {
    if (selectedRows.length > 0) {
      setAssignDialogOpen(true);
    }
  };

  // Handle opening the assign dialog for a single, unassigned questionnaire row
  const handleAssignSingle = (questionnaireId: string) => {
    setSelectedRows([questionnaireId]);
    setAssignDialogOpen(true);
  };

  const handleAssignQuestionnaires = async (patient: TRQUser, questionnaireIds: string[]) => {
    try {
      setLoading(true);

      // Get the current user's ID if available (for the assignedBy field)
      const currentUser = auth.currentUser;
      const assignedById = currentUser?.uid;

      // Process each questionnaire assignment in sequence
      for (const qId of questionnaireIds) {
        await assignQuestionnaireToPatient(qId, {
          patientUid: patient.uid,
          assignedBy: assignedById
        });

        // Current timestamp for local state updates
        const now = new Date();
        const timestamp = Timestamp.fromDate(now);

        // Update the local state to reflect the assignment with the new assignedTo property
        setQuestionnaires((prev) =>
          prev.map((q) =>
            q.id === qId
              ? {
                  ...q,
                  assignedPatient: patient.uid,
                  status: QuestionnaireStatus.Assigned,
                  assignedTo: {
                    patientId: patient.uid,
                    patientUid: patient.uid,
                    assignedAt: timestamp,
                    assignedBy: assignedById || undefined
                  }
                }
              : q
          )
        );
      }

      // Show success message or notification here if needed
      console.log(
        `Successfully assigned ${questionnaireIds.length} questionnaire(s) to patient ${patient.uid} (${patient.firstName} ${patient.lastName})`
      );
    } catch (error) {
      // Handle any errors that occurred during assignment
      console.error('Error assigning questionnaires:', error);
      // Show error message or notification here if needed
    } finally {
      setLoading(false);
      setAssignDialogOpen(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  // Handle review action
  const handleReview = (id: string) => {
    // Simple static permission check without dynamic context
    if (permissions.canUpdateQuestionnaire()) {
      navigate(`/trq/questionnaires/review/${id}`);
    }
  };

  const handleRespiratoryQuestionnaire = () => {
    // Navigate to the respiratory questionnaire template details page
    navigate('/trq/templates/respiratory-health-questionnaire-template/details');
  };

  // Filter questionnaires based on search term and status
  const filteredQuestionnaires = questionnaires.filter((questionnaire) => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch =
      questionnaire.title.toLowerCase().includes(searchLower) ||
      (questionnaire.description && questionnaire.description.toLowerCase().includes(searchLower));
    const matchesStatus = statusFilter === 'all' || questionnaire.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Custom toolbar with search, filter, and action buttons
  const CustomToolbar = () => (
    <QuestionnaireToolbar
      intl={intl}
      selectedRows={selectedRows}
      permissions={permissions}
      questionnaires={questionnaires}
      statusFilter={statusFilter}
      searchTerm={searchTerm}
      handleView={handleView}
      handleReview={handleReview}
      handleDelete={handleDelete}
      handleOpenAssignDialog={handleOpenAssignDialog}
      handleSearchChange={handleSearchChange}
      handleStatusFilterChange={handleStatusFilterChange}
      setTemplateDialogOpen={setTemplateDialogOpen}
      onRespiratoryQuestionnaireClick={handleRespiratoryQuestionnaire}
    />
  );

  // Define columns for the data grid
  const columns: GridColDef[] = [
    {
      field: 'title',
      headerName: intl.formatMessage({ id: 'title' }) || 'Title',
      flex: 1.5,
      minWidth: 200,
      headerAlign: 'center',
      align: 'center'
    },
    {
      field: 'patientId',
      headerName: intl.formatMessage({ id: 'assigned-to' }) || 'Assigned To',
      flex: 1,
      minWidth: 150,
      headerAlign: 'left',
      align: 'left',
      renderCell: (params: GridRenderCellParams<Questionnaire>) => {
        if (!params.row || !params.row.id) {
          return null;
        }

        // Get patient ID from either the patientId field or the assignedTo.patientId field
        const patientId = params.row.assignedPatient || params.row.assignedTo?.patientId;
        const questionnaireId = params.row.id;

        if (!patientId) {
          // Only show assign button if user has permission (static role check)
          if (permissions.canUpdateQuestionnaire()) {
            return (
              <Button
                variant="text"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleAssignSingle(questionnaireId);
                }}
                aria-label={intl.formatMessage({ id: 'assign-questionnaire' }) || 'Assign Questionnaire'}
                // Added dynamic data-testid here
                data-testid={`questionnaire-row-assign-button-${questionnaireId}`}
                sx={{
                  display: 'flex',
                  margin: 'auto',
                  minWidth: 'auto',
                  padding: '2px 8px'
                }}
              >
                {intl.formatMessage({ id: 'assign' }) || 'Assign'}
              </Button>
            );
          }
          return null;
        }

        const patientName = userMap[patientId];
        const displayName: string | null = patientName || patientId || null;

        // Show assignment details including when it was assigned if available
        const assignedAt = params.row.assignedTo?.assignedAt;
        const assignmentDate = assignedAt ? formatTimestamp(assignedAt) : '';

        return (
          <Stack spacing={0} alignItems="center">
            <Typography variant="body2" noWrap title={displayName ?? ''}>
              {displayName}
            </Typography>
            {assignmentDate && (
              <Typography variant="caption" color="textSecondary" noWrap>
                {intl.formatMessage({ id: 'assigned-on' }) || 'Assigned on'}: {assignmentDate}
              </Typography>
            )}
          </Stack>
        );
      }
    },
    {
      field: 'status',
      headerName: intl.formatMessage({ id: 'status' }) || 'Status',
      flex: 1,
      minWidth: 120,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params: GridRenderCellParams) => {
        const statusColors: { [key: string]: 'default' | 'warning' | 'success' | 'info' | 'primary' | 'secondary' | 'error' } = {
          created: 'default',
          assigned: 'secondary',
          'in-progress': 'warning',
          completed: 'success',
          reviewed: 'info'
        };
        const status = (params.value as string) || 'created';
        return (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%', height: '100%' }}>
            <Chip
              label={status ? (intl.formatMessage({ id: status }) || status) : 'Unknown'}
              color={statusColors[status] || 'default'}
              size="small"
              variant="filled"
              sx={
                status === 'in-progress'
                  ? {
                      color: '#212121', // dark text
                      backgroundColor: (theme) => theme.palette.warning.light,
                      fontWeight: 600,
                      border: '1px solid #fbc02d'
                    }
                  : undefined
              }
            />
          </Box>
        );
      }
    },
    {
      field: 'createdAt',
      headerName: intl.formatMessage({ id: 'created-at' }) || 'Created At',
      flex: 1,
      minWidth: 150,
      headerAlign: 'left',
      align: 'left',
      valueFormatter: (params: GridValueFormatterParams) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value);
      }
    },
    {
      field: 'completedAt',
      headerName: intl.formatMessage({ id: 'completed-at' }) || 'Completed At',
      flex: 1,
      minWidth: 150,
      headerAlign: 'left',
      align: 'left',
      valueFormatter: (params: GridValueFormatterParams) => {
        if (!params?.value) return '';
        return formatTimestamp(params.value);
      }
    },
    {
      field: 'description',
      headerName: intl.formatMessage({ id: 'description' }) || 'Description',
      flex: 2,
      minWidth: 250,
      sortable: false,
      filterable: false,
      headerAlign: 'center',
      align: 'center'
    }
  ];

  return (
    <MainCard>
      <Typography
        variant="body1"
        sx={{
          mb: 3,
          color: 'text.secondary',
          bgcolor: 'background.paper',
          p: 2,
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        Manage all questionnaires in the system from this central dashboard. Create new questionnaires from templates, assign them to
        patients, track their status, and review completed submissions. Use the filters and search to quickly find specific questionnaires.
      </Typography>

      {permissions.canListQuestionnaires() ? (
        <Box sx={{ height: 'auto', width: '100%' }}>
          <TrqDataGrid
            rows={filteredQuestionnaires}
            columns={columns}
            loading={loading}
            initialState={{
              pagination: {
                paginationModel: {
                  pageSize: 10
                }
              },
              sorting: {
                sortModel: [{ field: 'createdAt', sort: 'desc' }]
              }
            }}
            checkboxSelection
            onRowSelectionModelChange={handleSelectionChange}
            rowSelectionModel={selectedRows}
            slots={{
              toolbar: CustomToolbar
            }}
            sx={dataGridStyles}
            onRowClick={(params) => {
              if (permissions.canViewQuestionnaire()) {
                handleView(params.id.toString());
              }
            }}
          />
        </Box>
      ) : null}

      {permissions.canCreateQuestionnaire() && (
        <TemplateSelectionDialog open={templateDialogOpen} onClose={() => setTemplateDialogOpen(false)} onSelect={handleTemplateSelect} />
      )}

      {/* Conditionally render dialog - can open even if multiple selected */}
      {assignDialogOpen && permissions.canUpdateQuestionnaire() && (
        <AssignQuestionnaireDialog
          open={assignDialogOpen}
          onClose={() => setAssignDialogOpen(false)}
          onSubmit={handleAssignQuestionnaires}
          questionnairesToAssign={questionnaires.filter((q) => selectedRows.includes(q.id!))}
        />
      )}
    </MainCard>
  );
};

export default AllQuestionnaires;
