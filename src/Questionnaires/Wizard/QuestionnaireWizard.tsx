import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Box, Button, Container, Paper, Typography, CircularProgress, Alert } from '@mui/material';
import { useForm, FormProvider } from 'react-hook-form';
import { getQuestionnaireById } from 'Questionnaires/[services]/questionnaireService';
import { submitQuestionnaireResponses } from 'Questionnaires/[services]/questionnaireService';
import { Questionnaire } from 'Questionnaires/[types]/Questionnaire';
import { Response } from 'Questionnaires/[types]/Response'; // Import the Response type
import { useSnackbar } from 'notistack';
import { motion } from 'framer-motion';
import { QuestionnaireWizardProvider } from './QuestionnaireWizardContext';
import { AnimatedQuestionContainer } from './AnimatedQuestionContainer';
import { StepperNavigation } from './StepperNavigation';
import { CompletionAnimation } from './CompletionAnimation';
import { useKeyboardNavigation } from './useKeyboardNavigation';
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp';
import { useAuth } from 'Authentication/[contexts]/AuthContext'; // Use correct useAuth import
import { Timestamp } from 'firebase/firestore';
import { Role } from 'RBAC/[types]/Role';
import RespiratoryInstanceWizard from '../RespiratoryQuestionnaire/components/RespiratoryInstanceWizard';

interface FormData {
  [key: string]: any;
}

export const QuestionnaireWizard: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { currentUser, userData } = useAuth(); // Call useAuth as a hook
  const [questionnaire, setQuestionnaire] = useState<Questionnaire | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCompletion, setShowCompletion] = useState(false);

  const methods = useForm<FormData>({
    mode: 'onChange'
  });

  // Fetch the questionnaire instance data
  useEffect(() => {
    const fetchQuestionnaire = async () => {
      if (!id) return;
      try {
        const data = await getQuestionnaireById(id);
        if (!data) {
          setError('Questionnaire not found');
          return;
        }
        setQuestionnaire(data);
      } catch (error) {
        setError('Error loading questionnaire');
        enqueueSnackbar(`Error loading questionnaire: ${error}`, { variant: 'error' });
      } finally {
        setLoading(false);
      }
    };
    fetchQuestionnaire();
  }, [id, enqueueSnackbar]);

  // Check if this is a respiratory questionnaire and render the appropriate wizard
  const isRespiratoryQuestionnaire = questionnaire && (
    questionnaire.metadata?.wizardType === 'respiratory' ||
    questionnaire.templateId === 'respiratory-health-questionnaire-template' ||
    questionnaire.category === 'Respiratory Health'
  );

  // If this is a respiratory questionnaire, render the respiratory wizard
  if (isRespiratoryQuestionnaire) {
    return <RespiratoryInstanceWizard questionnaireId={id} questionnaire={questionnaire} />;
  }

  // Handle form submission
  const handleSubmit = async (data: FormData) => {
    if (!currentUser || !id) {
      enqueueSnackbar('You must be logged in to submit a questionnaire.', { variant: 'error' });
      return;
    }
    try {
      setIsSubmitting(true);

      // Map form data to response objects
      const responses: { [questionId: string]: Response } = {};
      Object.keys(data).forEach((questionId) => {
        responses[questionId] = {
          value: data[questionId],
          respondedAt: Timestamp.now()
        };
      });

      await submitQuestionnaireResponses({
        questionnaireId: id,
        userId: currentUser.uid,
        responses,
        submittedAt: new Date().toISOString()
      });

      // Show completion animation
      setShowCompletion(true);

      setTimeout(() => {
        enqueueSnackbar('Questionnaire submitted successfully', { variant: 'success' });
        setTimeout(() => {
          navigate('/trq/questionnaires/completed');
        }, 1500);
      }, 1000);
    } catch (error) {
      enqueueSnackbar(`Error submitting questionnaire: ${error}`, { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create the submission handler for the form
  const submissionHandler = methods.handleSubmit(handleSubmit);

  // Show loading state
  if (loading) {
    return (
      <Container maxWidth="sm" sx={{ pt: 6 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <CircularProgress />
          <Typography sx={{ mt: 2 }}>Loading questionnaire...</Typography>
        </Paper>
      </Container>
    );
  }

  // Show error state
  if (error || !questionnaire) {
    return (
      <Container maxWidth="sm" sx={{ pt: 6 }}>
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Alert severity="error">{error || 'Questionnaire not found.'}</Alert>
        </Paper>
      </Container>
    );
  }

  // Don't try to render if there are no questions
  if (questionnaire && (!questionnaire.questions || questionnaire.questions.length === 0)) {
    return (
      <Container maxWidth="md">
        <Box sx={{ my: 4 }}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h4" gutterBottom>
              {questionnaire.title}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" paragraph>
              {questionnaire.description}
            </Typography>
            <Alert severity="info">This questionnaire does not contain any questions.</Alert>
          </Paper>
          <Button onClick={() => navigate(userData?.role === Role.Patient ? '/trq/my-questionnaires' : '/trq/questionnaires')}>
            Back to Questionnaires
          </Button>
        </Box>
      </Container>
    );
  }

  // Wrap everything in QuestionnaireWizardProvider
  return (
    <QuestionnaireWizardProvider questions={questionnaire.questions || []}>
      <Container maxWidth="md">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
          <Box sx={{ my: 4 }}>
            <Paper
              sx={{
                p: 3,
                mb: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                borderRadius: '12px',
                transition: 'box-shadow 0.3s ease'
              }}
              elevation={3}
            >
              <Typography variant="h4" gutterBottom>
                {questionnaire.title}
              </Typography>
              <Typography variant="subtitle1" color="text.secondary" paragraph>
                {questionnaire.description}
              </Typography>
            </Paper>

            <FormProvider {...methods}>
              <form onSubmit={methods.handleSubmit(handleSubmit)}>
                <Paper
                  sx={{
                    p: 3,
                    mb: 3,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    borderRadius: '12px',
                    minHeight: '350px',
                    transition: 'all 0.3s ease',
                    position: 'relative'
                  }}
                  elevation={3}
                >
                  <AnimatedQuestionContainer control={methods.control} errors={methods.formState.errors} />
                  <CompletionAnimation isVisible={showCompletion} />
                </Paper>

                <StepperNavigation
                  onSubmit={methods.handleSubmit(handleSubmit)}
                  isValid={methods.formState.isValid}
                  isSubmitting={isSubmitting}
                />
              </form>
            </FormProvider>
          </Box>
        </motion.div>
        <KeyboardShortcutsHelp />
        {/* Move useKeyboardNavigation inside the provider */}
        {/*
          The following hook must be called inside the provider context.
          We use a custom component to call the hook.
        */}
        <KeyboardNavigationHandler onSubmit={submissionHandler} isValid={methods.formState.isValid} isSubmitting={isSubmitting} />
      </Container>
    </QuestionnaireWizardProvider>
  );
};

// Helper component to call the hook inside the provider
const KeyboardNavigationHandler = ({
  onSubmit,
  isValid,
  isSubmitting
}: {
  onSubmit: () => void;
  isValid: boolean;
  isSubmitting: boolean;
}) => {
  useKeyboardNavigation({ onSubmit, isValid, isSubmitting });
  return null;
};

export default QuestionnaireWizard;
