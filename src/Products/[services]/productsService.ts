import { collection, getDocs, getDoc, doc, addDoc, getFirestore } from 'firebase/firestore';

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  imageUrl?: string;
  questionnaireId?: string; // Link to questionnaire if needed
  complianceDetails?: string;
  // Extended properties for enhanced UI
  category?: string;
  rating?: number;
  reviewCount?: number;
  originalPrice?: number;
  isNew?: boolean;
  tags?: string[];
  stock?: number;
  features?: string[];
  quantity?: number; // Added for cart functionality
  createdAt?: Date;
  updatedAt?: Date;
  isActive?: boolean; // Controls product visibility
}

export const getProducts = async (): Promise<Product[]> => {
  const db = getFirestore();
  const colRef = collection(db, 'products');
  const snapshot = await getDocs(colRef);
  return snapshot.docs.map((docSnap) => ({ id: docSnap.id, ...docSnap.data() })) as Product[];
};

export const getProductById = async (id: string): Promise<Product | null> => {
  const db = getFirestore();
  const docRef = doc(db, 'products', id);
  const docSnap = await getDoc(docRef);
  if (!docSnap.exists()) return null;
  return { id: docSnap.id, ...docSnap.data() } as Product;
};

// Create a new product in Firestore
export const createProduct = async (db: any, product: Omit<Product, 'id'>): Promise<Product> => {
  const colRef = collection(db, 'products');
  const docRef = await addDoc(colRef, product);
  const docSnap = await getDoc(docRef);
  return { id: docSnap.id, ...docSnap.data() } as Product;
};
