import React, { useState } from 'react';
import { useCart, CartItem } from '../[contexts]/CartContext';
import {
  Box,
  Typography,
  Button,
  Divider,
  Paper,
  Stack,
  Grid,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Badge,
  Chip,
  TextField,
  Alert,
  LinearProgress,
  Tooltip,
  Collapse,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Fade,
  Zoom
} from '@mui/material';
import {
  ShoppingCartOutlined,
  LocalShipping,
  Security,
  CreditCard,
  Favorite,
  FavoriteBorder,
  Add,
  Remove,
  Delete,
  LocalOffer,
  Verified,
  Star,
  TrendingUp,
  Schedule,
  CheckCircle,
  ExpandMore,
  ExpandLess,
  ShoppingBag,
  Savings,
  LocalAtm,
  Timer
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';


export default function Cart() {
  const { items, clearCart, updateQuantity, removeFromCart } = useCart();
  const navigate = useNavigate();
  const theme = useTheme();
  const [promoCode, setPromoCode] = useState('');
  const [promoApplied, setPromoApplied] = useState(false);
  const [showPromoDetails, setShowPromoDetails] = useState(false);
  const [wishlistItems, setWishlistItems] = useState<string[]>([]);
  const [showRecommendations, setShowRecommendations] = useState(true);

  // Enhanced calculations
  const subtotal = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const totalSavings = items.reduce((sum, item) => sum + ((item.originalPrice || item.price) - item.price) * item.quantity, 0);
  const shipping = subtotal > 50 ? 0 : 8.99;
  const tax = subtotal * 0.08;
  const promoDiscount = promoApplied ? subtotal * 0.1 : 0;
  const total = subtotal + shipping + tax - promoDiscount;
  const freeShippingThreshold = 50;
  const freeShippingProgress = Math.min((subtotal / freeShippingThreshold) * 100, 100);

  // Mock recommended products
  const recommendedProducts = [
    { id: 'rec1', name: 'Wireless Headphones', price: 79.99, image: '/api/placeholder/100/100', rating: 4.5 },
    { id: 'rec2', name: 'Smart Watch', price: 199.99, image: '/api/placeholder/100/100', rating: 4.3 },
    { id: 'rec3', name: 'Laptop Stand', price: 29.99, image: '/api/placeholder/100/100', rating: 4.7 }
  ];

  const handleQuantityChange = (id: string, newQuantity: number) => {
    if (newQuantity > 0) {
      updateQuantity(id, newQuantity);
    }
  };

  const handleWishlist = (id: string) => {
    setWishlistItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const applyPromoCode = () => {
    if (promoCode.toLowerCase() === 'save10') {
      setPromoApplied(true);
      setShowPromoDetails(true);
    }
  };

  const EmptyCart = () => (
    <Fade in timeout={800}>
      <Paper 
        sx={{ 
          p: 8, 
          textAlign: 'center', 
          mt: 4, 
          background: `linear-gradient(135deg, ${theme.palette.primary.light}15 0%, ${theme.palette.secondary.light}15 100%)`,
          borderRadius: 3,
          border: `1px solid ${theme.palette.divider}`
        }} 
        elevation={0}
      >
        <Avatar 
          sx={{ 
            width: 120, 
            height: 120, 
            mx: 'auto', 
            mb: 3,
            bgcolor: theme.palette.primary.main + '20',
            color: theme.palette.primary.main
          }}
        >
          <ShoppingBag sx={{ fontSize: 60 }} />
        </Avatar>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Your Shopping Cart is Empty
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 400, mx: 'auto' }}>
          Looks like you haven't added any items to your cart yet. Start shopping to fill it up!
        </Typography>
        <Stack direction="row" spacing={2} justifyContent="center">
          <Button 
            variant="contained" 
            size="large" 
            onClick={() => navigate('/trq/products')}
            sx={{ 
              borderRadius: 2, 
              px: 4, 
              py: 1.5,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: theme.shadows[8]
              }
            }}
          >
            Continue Shopping
          </Button>
          <Button 
            variant="outlined" 
            size="large" 
            onClick={() => navigate('/trq/products')}
            sx={{ borderRadius: 2, px: 4, py: 1.5 }}
          >
            Browse Categories
          </Button>
        </Stack>
      </Paper>
    </Fade>
  );

  const CartItemComponent = ({ item }: { item: CartItem }) => (
    <Zoom in timeout={600}>
      <Card 
        sx={{ 
          mb: 2, 
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8]
          },
          borderRadius: 2,
          overflow: 'hidden'
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={3}>
              <Box sx={{ position: 'relative' }}>
                <CardMedia
                  component="img"
                  height="120"
                  image={item.image || '/api/placeholder/120/120'}
                  alt={item.name}
                  sx={{ 
                    borderRadius: 2,
                    objectFit: 'cover',
                    bgcolor: theme.palette.grey[100]
                  }}
                />
                {item.savings && (
                  <Chip
                    label={`Save $${item.savings.toFixed(2)}`}
                    size="small"
                    color="success"
                    sx={{ 
                      position: 'absolute', 
                      top: 8, 
                      left: 8,
                      fontWeight: 600
                    }}
                  />
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  {item.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  {item.description || 'Premium quality product with excellent features'}
                </Typography>
                
                <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
                  {item.category && (
                    <Chip label={item.category} size="small" variant="outlined" />
                  )}
                  {item.rating && (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Star sx={{ color: '#FFD700', fontSize: 16 }} />
                      <Typography variant="body2" sx={{ ml: 0.5 }}>
                        {item.rating}
                      </Typography>
                    </Box>
                  )}
                  {item.inStock !== false && (
                    <Chip 
                      icon={<CheckCircle />} 
                      label="In Stock" 
                      size="small" 
                      color="success" 
                      variant="outlined"
                    />
                  )}
                </Stack>
                
                {item.shippingTime && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocalShipping sx={{ fontSize: 16, mr: 1, color: theme.palette.success.main }} />
                    <Typography variant="body2" color="success.main">
                      {item.shippingTime}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'right' }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    ${item.price.toFixed(2)}
                  </Typography>
                  {item.originalPrice && item.originalPrice > item.price && (
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        textDecoration: 'line-through',
                        color: 'text.secondary'
                      }}
                    >
                      ${item.originalPrice.toFixed(2)}
                    </Typography>
                  )}
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Total: ${(item.price * item.quantity).toFixed(2)}
                  </Typography>
                </Box>
                
                <Stack direction="row" spacing={1} justifyContent="flex-end" alignItems="center" sx={{ mb: 2 }}>
                  <IconButton 
                    onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                    disabled={item.quantity <= 1}
                    size="small"
                    sx={{ 
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1
                    }}
                  >
                    <Remove fontSize="small" />
                  </IconButton>
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      minWidth: 40, 
                      textAlign: 'center', 
                      fontWeight: 600,
                      py: 0.5,
                      px: 1,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1
                    }}
                  >
                    {item.quantity}
                  </Typography>
                  <IconButton 
                    onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                    size="small"
                    sx={{ 
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 1
                    }}
                  >
                    <Add fontSize="small" />
                  </IconButton>
                </Stack>
                
                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Tooltip title="Add to Wishlist">
                    <IconButton 
                      onClick={() => handleWishlist(item.id)}
                      size="small"
                      sx={{ color: wishlistItems.includes(item.id) ? 'error.main' : 'text.secondary' }}
                    >
                      {wishlistItems.includes(item.id) ? <Favorite /> : <FavoriteBorder />}
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Remove from Cart">
                    <IconButton 
                      onClick={() => removeFromCart(item.id)}
                      size="small"
                      sx={{ color: 'error.main' }}
                    >
                      <Delete />
                    </IconButton>
                  </Tooltip>
                </Stack>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Zoom>
  );

  if (items.length === 0) {
    return (
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
        <EmptyCart />
      </Box>
    );
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h3" 
          gutterBottom 
          sx={{ 
            fontWeight: 700,
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            color: 'transparent',
            display: 'flex',
            alignItems: 'center',
            gap: 2
          }}
        >
          <ShoppingCartOutlined sx={{ color: theme.palette.primary.main }} />
          Shopping Cart
          <Badge badgeContent={items.length} color="primary" sx={{ ml: 1 }}>
            <Box />
          </Badge>
        </Typography>
        
        {/* Free Shipping Progress */}
        {subtotal < freeShippingThreshold && (
          <Alert 
            severity="info" 
            sx={{ 
              mt: 2, 
              borderRadius: 2,
              '& .MuiAlert-message': { width: '100%' }
            }}
          >
            <Box>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Add ${(freeShippingThreshold - subtotal).toFixed(2)} more to get <strong>FREE SHIPPING</strong>!
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={freeShippingProgress} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: theme.palette.grey[200],
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    background: `linear-gradient(45deg, ${theme.palette.success.main}, ${theme.palette.primary.main})`
                  }
                }}
              />
            </Box>
          </Alert>
        )}
      </Box>

      <Grid container spacing={4}>
        {/* Cart Items */}
        <Grid item xs={12} lg={8}>
          <Box>
            {items.map((item) => (
              <CartItemComponent key={item.id} item={item} />
            ))}
            
            {/* Recommendations */}
            {showRecommendations && (
              <Paper sx={{ p: 3, mt: 3, borderRadius: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    <TrendingUp sx={{ mr: 1, verticalAlign: 'middle' }} />
                    Recommended for You
                  </Typography>
                  <IconButton onClick={() => setShowRecommendations(false)}>
                    <ExpandLess />
                  </IconButton>
                </Box>
                <Grid container spacing={2}>
                  {recommendedProducts.map((product) => (
                    <Grid item xs={12} sm={4} key={product.id}>
                      <Card sx={{ borderRadius: 2, '&:hover': { transform: 'translateY(-2px)' } }}>
                        <CardMedia
                          component="img"
                          height="100"
                          image={product.image}
                          alt={product.name}
                        />
                        <CardContent sx={{ p: 2 }}>
                          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                            {product.name}
                          </Typography>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" color="primary" sx={{ fontWeight: 600 }}>
                              ${product.price}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Star sx={{ color: '#FFD700', fontSize: 14 }} />
                              <Typography variant="caption" sx={{ ml: 0.5 }}>
                                {product.rating}
                              </Typography>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            )}
          </Box>
        </Grid>

        {/* Order Summary */}
        <Grid item xs={12} lg={4}>
          <Paper 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              position: 'sticky',
              top: 20,
              background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.primary.main}05 100%)`
            }}
          >
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
              Order Summary
            </Typography>
            
            {/* Promo Code */}
            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                size="small"
                label="Promo Code"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <Button 
                      size="small" 
                      onClick={applyPromoCode}
                      disabled={!promoCode || promoApplied}
                    >
                      Apply
                    </Button>
                  )
                }}
                sx={{ mb: 1 }}
              />
              {promoApplied && (
                <Alert severity="success" sx={{ mt: 1 }}>
                  Promo code applied! You saved ${promoDiscount.toFixed(2)}
                </Alert>
              )}
            </Box>
            
            {/* Pricing Breakdown */}
            <Stack spacing={2} sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Subtotal ({items.length} items)</Typography>
                <Typography>${subtotal.toFixed(2)}</Typography>
              </Box>
              
              {totalSavings > 0 && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography color="success.main">
                    <Savings sx={{ fontSize: 16, mr: 1 }} />
                    Your Savings
                  </Typography>
                  <Typography color="success.main" sx={{ fontWeight: 600 }}>
                    -${totalSavings.toFixed(2)}
                  </Typography>
                </Box>
              )}
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>
                  <LocalShipping sx={{ fontSize: 16, mr: 1 }} />
                  Shipping
                </Typography>
                <Typography color={shipping === 0 ? 'success.main' : 'text.primary'}>
                  {shipping === 0 ? 'FREE' : `$${shipping.toFixed(2)}`}
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Tax</Typography>
                <Typography>${tax.toFixed(2)}</Typography>
              </Box>
              
              {promoApplied && (
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography color="success.main">
                    <LocalOffer sx={{ fontSize: 16, mr: 1 }} />
                    Promo Discount
                  </Typography>
                  <Typography color="success.main" sx={{ fontWeight: 600 }}>
                    -${promoDiscount.toFixed(2)}
                  </Typography>
                </Box>
              )}
            </Stack>
            
            <Divider sx={{ my: 2 }} />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Total
              </Typography>
              <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                ${total.toFixed(2)}
              </Typography>
            </Box>
            
            {/* Security Features */}
            <Box sx={{ mb: 3 }}>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Tooltip title="Secure Checkout">
                  <Chip 
                    icon={<Security />} 
                    label="Secure" 
                    size="small" 
                    color="success" 
                    variant="outlined"
                  />
                </Tooltip>
                <Tooltip title="Safe Payment">
                  <Chip 
                    icon={<CreditCard />} 
                    label="Safe Pay" 
                    size="small" 
                    color="primary" 
                    variant="outlined"
                  />
                </Tooltip>
                <Tooltip title="Fast Delivery">
                  <Chip 
                    icon={<Timer />} 
                    label="Fast" 
                    size="small" 
                    color="secondary" 
                    variant="outlined"
                  />
                </Tooltip>
              </Stack>
            </Box>
            
            {/* Action Buttons */}
            <Stack spacing={2}>
              <Button 
                variant="contained" 
                size="large" 
                fullWidth
                onClick={() => navigate('/trq/purchases/checkout')}
                sx={{ 
                  py: 1.5,
                  borderRadius: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
              >
                <CreditCard sx={{ mr: 1 }} />
                Proceed to Checkout
              </Button>
              
              <Button 
                variant="outlined" 
                size="large" 
                fullWidth
                onClick={() => navigate('/trq/products')}
                sx={{ 
                  py: 1.5,
                  borderRadius: 2,
                  borderWidth: 2,
                  '&:hover': {
                    borderWidth: 2
                  }
                }}
              >
                Continue Shopping
              </Button>
              
              <Button 
                variant="text" 
                size="small" 
                fullWidth
                onClick={clearCart}
                sx={{ 
                  color: 'error.main',
                  '&:hover': {
                    bgcolor: 'error.main',
                    color: 'white'
                  }
                }}
              >
                Clear Cart
              </Button>
            </Stack>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
