import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { useRole } from 'RBAC/[contexts]/RoleContext'; // Assuming RoleContext provides useRole
import { canAccessRoute } from 'RBAC/utils/route-permissions';
import ROUTES from 'Routing/appRoutes';
import { GuardProps } from '[types]/general';
import Loader from '[components]/Loader';
import ErrorFallback from '[components]/ErrorFallback';
import React from 'react';

// ==============================|| TRQ AUTH GUARD ||============================== //

/**
 * Authentication guard: ensures user is authenticated.
 * If not authenticated, redirects to login.
 * Usage: <TRQAuthGuard>...</TRQAuthGuard>
 */
export default function TRQAuthGuard({ children }: GuardProps) {
  const { isAuthenticated, isLoading: isAuthLoading, error: authError } = useAuth();
  const { role, isRoleLoading } = useRole(); // Get current user's role and loading state
  const location = useLocation(); // Get current location
  const [timedOut, setTimedOut] = React.useState(false);

  React.useEffect(() => {
    // Combined loading check for timeout
    if (!isAuthLoading && !isRoleLoading) return;
    const timer = setTimeout(() => setTimedOut(true), 10000); // 10s timeout for combined loading
    return () => clearTimeout(timer);
  }, [isAuthLoading, isRoleLoading]);

  if (authError) return <ErrorFallback error={authError} />;
  if (timedOut)
    return (
      <ErrorFallback
        error={new Error('Authentication or Role loading is taking too long. Please try again.')}
        resetErrorBoundary={() => window.location.reload()}
      />
    );
  // Show loader if either authentication or role is loading
  if (isAuthLoading || isRoleLoading) return <Loader />;
  if (!isAuthenticated) return <Navigate to={ROUTES.AUTH.LOGIN} replace />;

  // Role-based authorization check (now role is guaranteed to be loaded or defaulted)
  // const { role } = useRole(); // MOVED UP
  // const location = useLocation(); // MOVED UP

  if (!canAccessRoute(role, location.pathname)) {
    // If user's role is not allowed for the current path, redirect to unauthorized page
    console.warn(`Role-based access denied for role '${role}' to path '${location.pathname}'. Redirecting to unauthorized.`);
    return <Navigate 
      to={ROUTES.AUTH.UNAUTHORIZED} 
      replace 
      state={{ 
        from: location.pathname,
        role: role 
      }} 
    />;
  }

  return <>{children}</>;
}
