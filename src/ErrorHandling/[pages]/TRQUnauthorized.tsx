import React, { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { useNavigate, useLocation } from 'react-router-dom';

// material-ui
import { Grid, Typography, Button, Stack, Box, Paper, Divider, Chip } from '@mui/material';

// project imports
import MainCard from '[components]/cards/MainCard';
import AnimateButton from '[components]/extended/AnimateButton';
import { useRole } from 'RBAC/[contexts]/RoleContext';
import { Role } from 'RBAC/[types]/Role';
import { hasPermission, getRoleDisplayName } from 'RBAC/[services]/roleService';
import * as NoPermission from 'Authentication/[components]/NoPermission';
import { useAuth } from 'Authentication/[contexts]/AuthContext';
import { getDashboardUrlForRole } from 'Routing/appRoutes';
import { canAccessRoute } from 'RBAC/utils/route-permissions';

// assets
import WarningIcon from '@mui/icons-material/Warning';
import HomeIcon from '@mui/icons-material/Home';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface LocationState {
  resource?: string;
  action?: string;
  role?: string;
  from?: string;
  error?: string;
}

const TRQUnauthorized = () => {
  const intl = useIntl();
  const navigate = useNavigate();
  const location = useLocation();
  const { role: currentRole } = useRole();
  const { userData } = useAuth(); // Get user from auth context
  const [requiredRoles, setRequiredRoles] = useState<Role[]>([]);

  // Check if state contains resource info
  const locationState = (location.state as LocationState) || {};
  const { resource, action, role, from, error } = locationState;

  // Log component mounting and state data
  useEffect(() => {
    console.log('[TRQUnauthorized] Component mounted');
    console.log('[TRQUnauthorized] Current path:', location.pathname);
    console.log('[TRQUnauthorized] Location state:', locationState);

    // Determine which roles would have access to this resource/action or path
    if (resource && action) {
      const allRoles = Object.values(Role);
      const roles = allRoles.filter((r) => hasPermission(r, action, resource));
      setRequiredRoles(roles);
    } else {
      // If we don't have specific resource/action, check which roles can access the current path
      const currentPath = from || location.pathname;
      const allRoles = Object.values(Role);
      const allowedRoles = allRoles.filter((r) => canAccessRoute(r, currentPath));
      console.log(`[TRQUnauthorized] Checking access for path: ${currentPath}`);
      console.log(`[TRQUnauthorized] Allowed roles for path:`, allowedRoles);
      setRequiredRoles(allowedRoles);
    }
  }, [location, locationState, resource, action, from]);

  // Create a user-friendly message about the resource
  const resourceMessage = resource ? `${action ? `${action} ` : ''}${resource}` : '';

  // Build a more detailed error message if we have the info
  const detailedError = error || (role && resource && action ? `${role} does not have permission to ${action} ${resource}` : '');
  
  // Generate a fallback message when resource/action are not provided
  const currentPath = from || location.pathname;
  const fallbackMessage = currentPath ? `Access to ${currentPath} is restricted` : 'Access to this resource is restricted';

  const goBack = () => {
    navigate(-1);
  };

  const goHome = () => {
    // Cast user role to Role type to satisfy the helper function's parameter type
    const dashboardPath = getDashboardUrlForRole(userData?.role as Role | null | undefined);
    navigate(dashboardPath);
  };

  // If resource and action are provided, use the standardized NoPermission component
  if (resource && action && !from && !error) {
    return (
      <NoPermission.default
        resource={resource}
        action={action}
        message={
          intl.formatMessage({ id: 'error.unauthorized.message' }, { resource: resourceMessage }) ||
          `You don't have permission to access ${resourceMessage ? `the ${resourceMessage}` : 'this resource'}.`
        }
        // Pass the dynamically determined path to NoPermission
        redirectPath={getDashboardUrlForRole(userData?.role as Role | null | undefined)}
        redirectLabel={intl.formatMessage({ id: 'button.go-home' }) || 'Go to Dashboard'}
      />
    );
  }

  // For more complex cases (like showing required roles, error details, etc.), use the full UI
  return (
    <MainCard>
      <Grid container justifyContent="center" alignItems="center" spacing={2}>
        <Grid item xs={12} textAlign="center">
          <Box sx={{ my: 3 }}>
            <WarningIcon color="error" sx={{ fontSize: '5rem', mb: 2 }} />
            <Typography variant="h1" color="error">
              {intl.formatMessage({ id: 'error.unauthorized.title' }) || '401'}
            </Typography>
            <Typography variant="h2" sx={{ my: 2 }}>
              {intl.formatMessage({ id: 'error.unauthorized.heading' }) || 'Unauthorized Access'}
            </Typography>
            <Typography variant="body1" sx={{ mb: 3, maxWidth: '800px', mx: 'auto', color: 'text.primary' }}>
              {intl.formatMessage({ id: 'error.unauthorized.message' }, { resource: resourceMessage }) ||
                `You don't have permission to access ${resourceMessage ? `the ${resourceMessage}` : 'this resource'}. Please contact your administrator if you believe this is a mistake.`}
            </Typography>
          </Box>
        </Grid>

        <Grid item xs={12} md={8}>
          <Paper
            elevation={3}
            sx={{
              p: 2,
              mb: 3,
              backgroundColor: (theme) => (theme.palette.mode === 'dark' ? 'error.dark' : 'error.light'),
              color: (theme) => theme.palette.error.contrastText,
              borderRadius: 1
            }}
          >
            <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
              <ErrorOutlineIcon />
              <Typography variant="body1">
                {detailedError || (resource && action ? `Access to ${action} ${resource} is restricted.` : fallbackMessage)}
              </Typography>
            </Stack>

            <Divider sx={{ my: 1.5, borderColor: 'error.dark' }} />

            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Your current role:
                <Chip label={currentRole ? getRoleDisplayName(currentRole) : 'Unknown'} color="default" size="small" sx={{ ml: 1 }} />
              </Typography>

              <Typography variant="subtitle2" sx={{ mt: 1, mb: 1 }}>
                Required role{requiredRoles.length !== 1 ? 's' : ''}:
              </Typography>

              <Stack direction="row" spacing={1} flexWrap="wrap">
                {requiredRoles.length > 0 ? (
                  requiredRoles.map((role) => (
                    <Chip key={role} label={getRoleDisplayName(role)} color="primary" size="small" sx={{ mb: 0.5 }} />
                  ))
                ) : (
                  <Typography variant="body2">No roles have access to this resource</Typography>
                )}
              </Stack>

              {!resource && !action && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Please contact your administrator for access to this page.
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>

        {from && (
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 2, mb: 3 }}>
              <Typography variant="body2" color="textSecondary">
                Attempted access to:{' '}
                <Typography component="span" fontWeight="bold">
                  {from}
                </Typography>
              </Typography>
            </Paper>
          </Grid>
        )}

        <Grid item xs={12}>
          <Stack direction="row" justifyContent="center" spacing={2}>
            <AnimateButton>
              <Button variant="outlined" size="large" onClick={goBack} startIcon={<ArrowBackIcon />}>
                {intl.formatMessage({ id: 'button.go-back' }) || 'Go Back'}
              </Button>
            </AnimateButton>
            <AnimateButton>
              <Button variant="contained" size="large" onClick={goHome} startIcon={<HomeIcon />}>
                {intl.formatMessage({ id: 'button.go-home' }) || 'Go to Dashboard'}
              </Button>
            </AnimateButton>
          </Stack>
        </Grid>
      </Grid>
    </MainCard>
  );
};

export default TRQUnauthorized;
