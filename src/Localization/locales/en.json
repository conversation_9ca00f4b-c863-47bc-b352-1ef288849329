{"dashboard": "Admin Dashboard", "default": "<PERSON><PERSON><PERSON>", "analytics": "Analytics", "Site Analytics": "Site Analytics", "widget": "Widget", "statistics": "Statistics", "data": "Data", "chart": "Chart", "application": "Application", "users": "Users", "user": "User", "posts": "Posts", "social-profile": "Social Profile", "account-profile": "Account Profile", "follower": "Follower", "friends": "Friends", "gallery": "Gallery", "friend-request": "Friend Request", "profile 01": "Profile 01", "profile 02": "Profile 02", "profile 03": "Profile 03", "cards": "Cards", "list": "List", "create": "Create", "order-details": "Order Details", "style 01": "Style 01", "style 02": "Style 02", "style 03": "Style 03", "customer": "Customer", "customer-list": "Customer List", "order-list": "Order List", "create-invoice": "Create Invoice", "product": "Product", "product-review": "Product Review", "chat": "Cha<PERSON>", "mail": "Mail", "contact": "Contact", "calendar": "Calendar", "kanban": "Ka<PERSON><PERSON>", "board": "Board", "backlogs": "Backlogs", "taskboard": "Taskboard", "e-commerce": "E-commerce", "products": "Products", "product-details": "Product Details", "product-list": "Product List", "checkout": "Checkout", "invoice": "Invoice", "client": "Client", "item": "Items", "payment": "Payment", "details": "Details", "edit": "Edit", "crm": "CRM", "lead-management": "Lead Management", "loading": "Loading", "overview": "Overview", "lead-list": "Lead List", "contact-management": "Contact Management", "contact-card": "Contact Card", "contact-list": "Contact List", "reminders-followup": "Reminders Followup", "communication-history": "Communication History", "sales-management": "Sales Management", "statement": "Statement", "refund": "Refund", "earning": "Earning", "blog": "Blog", "general-settings": "General Settings", "blog-list": "Blog List", "blog-details": "Blog Details", "add-new": "Add New", "forms": "Forms", "components": {"AddTeamMemberModal": {"form": {"role": {"admin": "Admin", "superAdmin": "Super Admin"}}}}, "autocomplete": "Autocomplete", "button": {"go-back": "Go Back", "go-home": "Go to Dashboard"}, "checkbox": "Checkbox", "date-time": "Date & Time", "radio": "Radio", "slider": "Slide<PERSON>", "switch": "Switch", "text-field": "Text Field", "plugins": "Plugins", "mask": "Mask", "clipboard": "Clipboard", "recaptcha": "reCaptcha", "wysiwug-editor": "Wysiwug Editor", "modal": "Modal", "tooltip": "<PERSON><PERSON><PERSON>", "dropzone": "Dropzone", "table": "Table", "table-basic": "Basic Table", "table-dense": "Dense Table", "table-enhanced": "Enhanced Tables", "table-data": "Data Table", "table-customized": "Custom Table", "table-sticky-header": "Fixed Header", "table-collapse": "Collapse Table", "data-grid": "Data Grid", "data-grid-basic": "Basic", "data-grid-inline-editing": "Inline Editing", "data-grid-column-groups": "Column Groups", "data-grid-save-restore": "Save & Restore", "data-grid-quick-filter": "Quick Filter", "data-grid-column-visibility": "Column Visibility", "data-grid-column-virtualization": "Column Virtualization", "data-grid-column-menu": "<PERSON><PERSON><PERSON>", "charts": "Charts", "apexchart": "Apexchart", "organization-chart": "Organization Chart", "forms-validation": "Forms Validation", "forms-wizard": "Forms Wizard", "layouts": "Layouts", "multi-column-forms": "Multi Column Forms", "action-bar": "Action Bar", "sticky-action-bar": "Sticky Action Bar", "ui-element": "UI Element", "basic": "Basic", "basic-caption": "8+ Basic Components", "accordion": "Accordion", "avatar": "Avatar", "badges": "Badges", "breadcrumb": "Breadcrumb", "chip": "Chip", "tabs": "Tabs", "advance": "Advance", "alert": "<PERSON><PERSON>", "dialog": "Dialog", "pagination": "Pagination", "progress": "Progress", "rating": "Rating", "snackbar": "Snackbar", "skeleton": "Skeleton", "speeddial": "Speeddial", "timeline": "Timeline", "toggle-button": "Toggle <PERSON>", "treeview": "Treeview", "pages": "Pages", "pages-caption": "Prebuild Pages", "authentication": "Authentication", "authentication 1": "Authentication 1", "authentication 2": "Authentication 2", "authentication 3": "Authentication 3", "login": "<PERSON><PERSON>", "register": "Register", "forgot-password": "Forgot Password", "check-mail": "Check Mail", "reset-password": "Reset Password", "code-verification": "Code Verification", "pricing": "Pricing", "price 01": "Price 01", "price 02": "Price 02", "maintenance": "Maintenance", "map": "Map", "error-404": "Error 404", "error-500": "Error 500", "coming-soon": "Coming Soon", "coming-soon 01": "Coming Soon 01", "coming-soon 02": "Coming Soon 02", "under-construction": "Under Construction", "landing": "Landing", "contact-us": "Contact US", "faqs": "FAQs", "privacy-policy": "Privacy Policy", "utilities": "Utilities", "typography": "Typography", "color": "Color", "shadow": "Shadow", "icons": "Icons", "tabler-icons": "Tabler Icons", "material-icons": "Material Icons", "animation": "Animation", "grid": "Grid", "others": "Others", "menu-level": "Menu Levels", "level 1": "Level 1", "level 2": "Level 2", "level 3": "Level 3", "menu-level-subtitle": "Sub Caption Levels", "menu-level-subtitle-caption": "Caption Collapse", "menu-level-subtitle-item": "Caption Item", "menu-level-subtitle-collapse": "Sub Collapse Caption", "menu-level-subtitle-sub-item": "Sub Item Caption", "disabled-menu": "Disabled <PERSON><PERSON>", "oval-chip-menu": "Oval Chip", "coded": "Coded", "c": "C", "outlined": "Outlined", "sample-page": "<PERSON><PERSON>", "documentation": "Documentation", "roadmap": "Roadmap", "title": "Title", "home": "Home", "change": "Change Language", "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiatnulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollitanim id est laborum.", "account-settings": "Account settings", "logout": "Logout", "more-items": "More Items", "all-users": "All Users", "user-details": "User Details", "back-to-users": "Back to Users", "add-user": "Add User", "add-new-user": "Add New User", "add-user-description": "Fill in the details to add a new user to the system.", "view": "View", "delete": "Delete", "cancel": "Cancel", "add": "Add", "save": "Save", "name": "Name", "email": "Email", "role": "Role", "questionnaires": "Questionnaires", "purchases": "Purchases", "no-users-found": "No users found", "first-name": "First Name", "last-name": "Last Name", "username": "Username", "phone": "Phone", "company": "Company", "first-name-required": "First name is required", "last-name-required": "Last name is required", "username-required": "Username is required", "valid-email-required": "Valid email is required", "user-added-successfully": "User added successfully", "error-adding-user": "Error adding user", "user-updated-successfully": "User updated successfully", "error-updating-user": "Error updating user: {error}", "user-deleted-successfully": "Deleted {count, plural, one {user} other {# users}} successfully.", "error-deleting-user": "Error deleting user(s).", "delete-user-confirmation": "Are you sure you want to delete {count, plural, one {the selected user} other {# selected users}}? This action cannot be undone.", "user-id-not-provided": "User ID not provided", "user-not-found": "User not found", "error-fetching-user": "Error fetching user: {error}", "not-available": "N/A", "invalid-date": "Invalid date", "registration-date": "Registration Date", "last-login": "Last Login", "status": "Status", "active": "Active", "inactive": "Inactive", "address": "Address", "date-created": "Date Created", "recent-activity": "Recent Activity", "personal-details": "Personal Details", "questionnaire-history": "Questionnaire History", "purchase-history": "Purchase History", "no-questionnaires-found": "No questionnaires found", "no-purchases-found": "No purchases found", "role-admin": "Administrator", "role-app-admin": "Application Administrator", "role-doctor": "Doctor", "role-patient": "Patient", "role-unknown": "Unknown Role", "title-questionnaire": "Questionnaire", "title-purchase": "Purchase", "created-on": "Created on", "purchased-on": "Purchased on", "no-description-available": "No description available", "price": "Price", "quantity": "Quantity", "total": "Total", "action": "Action", "save-changes": "Save Changes", "discard": "Discard", "save-success": "Changes saved successfully", "save-error": "Error saving changes", "all-clients": "All Clients", "add-client": "Add Client", "add-new-client": "Add New Client", "add-client-description": "Fill in the details to add a new client to the system.", "no-clients-found": "No clients found", "company-name": "Company Name", "company-name-required": "Company name is required", "state": "State", "city": "City", "postal-code": "Postal Code", "country": "Country", "notes": "Notes", "actions": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete", "list": "List", "manage": "Manage"}, "action-list": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete", "list": "List", "manage": "Manage", "view": "View", "edit": "Edit", "cancel": "Cancel", "add": "Add", "save": "Save", "back": "Back", "remove": "Remove", "confirm": "Confirm", "discard": "Discard"}, "confirm-delete": "Confirm Delete", "delete-client-confirmation": "Are you sure you want to delete {client}? This action cannot be undone.", "client-added-successfully": "Client added successfully", "error-adding-client": "Error adding client", "client-deleted-successfully": "Client deleted successfully", "error-deleting-client": "Error deleting client", "all-clinics": "All Clinics", "add-clinic": "Add Clinic", "add-new-clinic": "Add New Clinic", "add-clinic-description": "Fill in the details to add a new clinic to the system.", "no-clinics-found": "No clinics found", "clinic-name": "Clinic Name", "clinic-name-required": "Clinic name is required", "clinic-added-successfully": "Clinic added successfully", "error-adding-clinic": "Error adding clinic", "clinic-deleted-successfully": "Clinic deleted successfully", "error-deleting-clinic": "Error deleting clinic", "delete-clinic-confirmation": "Are you sure you want to delete {clinic}? This action cannot be undone.", "street": "Street", "zip-code": "Zip Code", "back": "Back", "error-fetching-clinics": "Error fetching clinics: {error}", "clinic-id-not-provided": "Clinic ID not provided", "clinic-not-found": "Clinic not found", "error-fetching-clinic": "Error fetching clinic: {error}", "clinic-updated-successfully": "Clinic updated successfully", "error-updating-clinic": "Error updating clinic: {error}", "back-to-clinics": "Back to Clinics", "clinic-details": "Clinic Details", "page-not-found": "Page Not Found", "page-moved-removed": "The page may have been moved or removed", "support": "Support", "role-client": "Client", "medical-assessment": "Medical Assessment", "respirator-clearance": "Respirator Clearance", "not-cleared": "Not Cleared (0)", "partially-cleared": "Partially Cleared (1)", "mostly-cleared": "Mostly Cleared (2)", "fully-cleared": "<PERSON><PERSON> Cleared (3)", "clearance-description": "Clearance Description", "clearance-based-on": "Clearance Based On", "follow-up-required": "Follow-up Required", "follow-up-description": "Follow-up Description", "workload-limitation": "Workload Limitation (%)", "category": "Category", "severity": "Severity", "low": "Low", "medium": "Medium", "high": "High", "description": "Description", "remove": "Remove", "user-id": "User ID", "created-at": "Created At", "last-updated": "Last Updated", "patient-id": "Patient ID", "associated-user": "Associated User", "no-questionnaires": "No questionnaires available", "date": "Date", "male": "Male", "female": "Female", "other": "Other", "gender": "Gender", "yes": "Yes", "no": "No", "created": "Created", "started": "Started", "resources": {"users": "Users", "questionnaires": "Questionnaires", "patients": "Patients", "reports": "Reports", "clinic_settings": "Clinic Settings", "system_settings": "System Settings", "own_profile": "Own Profile", "own_questionnaires": "Own Questionnaires", "own_reports": "Own Reports"}, "enums": {"clearance": {"not_cleared": "Not Cleared (0)", "partially_cleared": "Partially Cleared (1)", "mostly_cleared": "Mostly Cleared (2)", "fully_cleared": "<PERSON><PERSON> Cleared (3)"}, "severity": {"low": "Low", "medium": "Medium", "high": "High"}, "questionnaire_status": {"created": "Created", "in_progress": "In Progress", "completed": "Completed", "reviewed": "Reviewed"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}}, "clients": "Clients", "patient": "Patient", "settings": "Settings", "user-management": "User Management", "clinics": "Clinics", "patients": "Patients", "compliance-reports": "Compliance Reports", "templates": "Templates", "trq-system": "TRQ System", "client-management": "Client Management", "patient-management": "Patient Management", "clinic-management": "Clinic Management", "system-settings": "System Settings", "template-management": "Template Management", "questionnaire-management": "Questionnaire Management", "compliance-report-management": "Compliance Report Management", "patient-home": "Patient Home", "all-patients": "All Patients", "back-to-patients": "Back to Patients", "all-questionnaires": "All Questionnaires", "search-purchases": "Search purchases...", "generate-sample-reports": "Generate Sample Compliance Reports", "back-to-questionnaires": "Back to Questionnaires", "no-purchases": "No purchases available", "TRQ System": "TRQ System", "Dashboard": "Dashboard", "Patient Home": "Patient Home", "Questionnaires": "Questionnaires", "Templates": "Templates", "Compliance Reports": "Compliance Reports", "Patients": "Patients", "Clients": "Clients", "Clinics": "Clinics", "User Management": "User Management", "All Users": "All Users", "Purchases": "Purchases", "All Purchases": "All Purchases", "Settings": "Settings", "completed-at": "Completed At", "select-template": "Select Template", "start-questionnaire": "Start Questionnaire", "search": "Search", "all": "All", "in-progress": "In Progress", "completed": "Completed", "reviewed": "Reviewed", "assigned": "Assigned", "report-id": "Report ID", "doctor": "Doctor", "created-date": "Created Date", "follow-up": "Follow Up", "filters": "Filters", "filter-by": "Filter by", "clear-filters": "Clear Filters", "apply-filters": "Apply Filters", "filter-results": "Filter Results", "no-filters-applied": "No filters applied", "filter-status": "Filter by Status", "filter-date": "Filter by Date", "filter-doctor": "Filter by Doctor", "filter-patient": "Filter by Patient", "filter-client": "Filter by Client", "filter-clearance": "Filter by Clearance Level", "filter-severity": "Filter by Severity Level", "date-of-birth": "Date of Birth", "delete-patient-confirmation": "Are you sure you want to delete {patient}? This action cannot be undone.", "patient-list-coming-soon": "Patient list functionality coming soon", "medical-evaluation": "Medical Evaluation", "respirator-clearance-required": "Respirator clearance is required", "clearance-based-on-required": "Clearance basis is required", "category-required": "Category is required", "description-required": "Description is required", "severity-required": "Severity is required", "error-updating-report": "Error updating report", "edit-compliance-report": "Edit Compliance Report", "review-compliance-report": "Review Compliance Report", "report-details": "Report Details", "report-not-found": "Report not found", "error-fetching-report": "Error fetching report", "add-medical-finding": "Add Medical Finding", "no-medical-findings": "No medical findings recorded", "save-draft": "Save Draft", "deny": "<PERSON><PERSON>", "approve-with-suggestions": "Approve with Suggestions", "approve": "Approve", "add-patient": "Add Patient", "client-id": "Client ID", "add-new-patient": "Add New Patient", "add-patient-description": "Fill in the details to add a new patient to the system.", "patient-added-successfully": "Patient added successfully", "error-adding-patient": "Error adding patient", "patient-updated-successfully": "Patient updated successfully", "error-updating-patient": "Error updating patient: {error}", "patient-id-not-provided": "Patient ID not provided", "error-fetching-patient": "Error fetching patient: {error}", "client-name": "Client Name", "total-quantity": "Total Quantity", "total-price": "Total Price", "payment-status": "Payment Status", "purchase-date": "Purchase Date", "create-purchase": "Create Purchase", "all-statuses": "All Statuses", "compliance-report-details": "Compliance Report Details", "review": "Review", "view-pdf": "View PDF", "signed-date": "Signed Date", "questionnaire": "Questionnaire", "back-to-reports": "Back to Reports", "operator": "Operator", "unknown": "Unknown", "showing-reports": "Showing {count} of {total} Reports", "assigned-to": "Assigned To", "add-finding": "Add Finding", "assigned-doctor": "Assigned Doctor", "no-address-available": "No address information available", "add-comment": "Add Comment", "enter-your-comment": "Enter your comment here...", "questionnaire-not-found": "Questionnaire not found", "error": {"unauthorized": {"title": "401", "heading": "Unauthorized Access", "message": "You don't have permission to access {resource}. Please contact your administrator if you believe this is a mistake."}, "fetch": {"doctors": "Failed to fetch doctors."}}, "respirator-clearance-error": "Respirator clearance must be selected", "workload-limitation-error": "Workload limitation must be between 0 and 100", "follow-up-description-required": "Follow-up description is required when follow-up is enabled", "sample-reports-description": "This utility will generate sample compliance reports for testing purposes. It will create reports with different statuses (draft, pending review, completed) using existing patients, doctors, and questionnaires in the database.", "generating": "Generating...", "generate-reports": "Generate Sample Reports", "error-generating-reports": "Error generating reports", "reports-generated-success": "Successfully generated {count} sample reports", "generated-reports": "Generated Reports", "report": "Report", "client-id-missing": "Client ID is missing", "error-fetching-client-data": "Error fetching client data", "client-info": "Client Information", "contact-name": "Contact Name", "associated-patients": "Associated Patients", "no-associated-patients": "No patients found for this client", "purchase-list-coming-soon": "Purchase history coming soon", "firebase-uid": "Firebase UID", "clear": "Clear", "compliance-reports-title": "Compliance Reports", "draft": "Draft", "finalized": "Finalized", "archived": "Archived", "search-placeholder": "Search by ID, patient, or doctor", "contact-email": "Contact Email", "contact-phone": "Contact Phone", "city-state": "City, State", "edit-clinic": "Edit Clinic", "administrators": "Administrators", "no-administrators": "No administrators assigned to this clinic", "none": "None", "edit-patient": "<PERSON>", "roles": {"admin_description": "Full system administrator with all permissions", "clinic_admin_description": "Clinic administrator with clinic-level permissions", "client_description": "Client with limited data access", "doctor_description": "Medical doctor with clinical permissions", "patient_description": "Patient with own data access only"}, "clinic-info": "Clinic Information", "respirator-clearance-description": "Respirator Clearance Description", "medical-findings": "Medical Findings", "create-from-template": "Create from Template", "create-questionnaire": "Create Questionnaire", "assign": "Assign", "assign-questionnaire": "Assign Questionnaire", "assigned-on": "Assigned On", "admin-id-not-provided": "Admin ID not provided", "admin-not-found": "Admin not found", "user-not-admin": "User is not a clinic admin", "error-fetching-admin": "Error fetching admin: {error}", "no-permission-view-admin": "You do not have permission to view admin details", "admin-updated-successfully": "Admin updated successfully", "error-updating-admin": "Error updating admin: {error}", "clinic-admin-details": "Clinic Administrator Details", "clinic-admin-dashboard": "Clinic Admin Dashboard", "add-administrator": "Add Administrator", "all-clinic-admins": "All Clinic Admins", "administrator-permissions": "Administrator Permissions", "administrators-added-successfully": "Administrators added successfully", "account-info": "Account Information", "active-status": "Active Status", "activity": "Activity", "adding": "Adding", "apply": "Apply", "assigned-at": "Assigned At", "assigned-clinics": "Assigned Clinics", "assigned.doctor": "Assigned Doctor", "assign.questionnaire.title": "Assign Questionnaire", "assigning.multiple": "Assigning Multiple", "assigning.single": "Assigning Single", "auth-uid": "Authentication UID", "back-to-clients": "Back to Clients", "browse-products": "Browse Products", "clear-search": "Clear Search", "client-details": "Client Details", "client-not-found": "Client not found", "client-required": "Client is required", "client.company": "Client Company", "clinic": "Clinic", "clinic-admin": "Clinic Admin", "column": "Column", "completed-purchases": "Completed Purchases", "continue": "Continue", "date-of-birth-required": "Date of birth is required", "delete-multiple-admins-confirmation": "Are you sure you want to delete the selected administrators?", "delete-multiple-patients-confirmation": "Are you sure you want to delete the selected patients?", "dob": "Date of Birth", "doctor-details": "Doctor <PERSON>", "doctor-id": "Doctor ID", "error-fetching-questionnaire": "Error fetching questionnaire", "error-fetching-users": "Error fetching users", "error.fetch.assignment.data": "Failed to fetch assignment data", "error.unauthorized.title": "401", "error.unauthorized.heading": "Unauthorized Access", "error.unauthorized.message": "You don't have permission to access {resource}. Please contact your administrator if you believe this is a mistake.", "doctor-selection-required": "Doctor selection is required", "email-required": "Email is required", "error-adding-administrators": "Error adding administrators", "error-sending-reminders": "Error sending reminders", "filter-by-status": "Filter by Status", "filter-options": "Filter Options", "form-validation": "Form Validation", "invalid-email": "Invalid email address", "invalid-input": "Invalid input", "loading-data": "Loading data...", "loading-users": "Loading users...", "modal-close": "Close Modal", "modal-confirm": "Confirm", "no-data": "No data available", "no-data-found": "No data found", "no-items": "No items", "no-results": "No results found", "not-assigned": "Not assigned", "not-available-short": "N/A", "password-required": "Password is required", "questionnaire-assigned": "Questionnaire assigned successfully", "questionnaire-id": "Questionnaire ID", "questionnaire-status": "Questionnaire Status", "required-field": "This field is required", "search-patients": "Search patients...", "search-questionnaires": "Search questionnaires...", "select-all": "Select All", "select-none": "Select None", "success-message": "Operation completed successfully", "validation-error": "Validation error", "view-details": "View Details", "workspace": "Workspace", "yes-no": "Yes/No"}