{"dashboard": "Tableau de bord", "default": ",Défaut", "analytics": "Analytique", "widget": "Widget", "statistics": "Statistiques", "data": "<PERSON><PERSON><PERSON>", "chart": "Graphique", "application": "Application", "users": "Utilisatrices", "user": "Utilisatrice", "posts": "Messages", "social-profile": "Profil social", "account-profile": ",Profil du compte", "follower": "<PERSON><PERSON>le", "friends": "<PERSON><PERSON>", "gallery": "Galerie", "friend-request": "<PERSON><PERSON><PERSON> d'ami", "profile": "Profil", "profile 01": "Profil 01", "profile 02": "Profil 02", "profile 03": "Profil 03", "cards": "<PERSON><PERSON>", "list": "Lister", "create": "<PERSON><PERSON><PERSON>", "order-details": "<PERSON><PERSON><PERSON> de la commande", "style": "Style", "style 01": "Style 01", "style 02": "Style 02", "style 03": "Style 03", "customer": "Client", "customer-list": "Liste de clients", "order-list": "Liste de commande", "create-invoice": "<PERSON><PERSON>er une facture", "product": "Produit", "product-review": "Évaluation du produit", "chat": "Discuter", "mail": "Poster", "contact": "<PERSON>er", "calendar": "<PERSON><PERSON><PERSON>", "kanban": "Ka<PERSON><PERSON>", "board": "Conseil", "backlogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskboard": "Tableau des tâches", "e-commerce": "Commerce électronique", "products": "Des produits", "product-details": "détails du produit", "product-list": "Liste de produits", "checkout": "Vérifier", "invoice": "Facture", "client": "Cliente", "payment": "Paiement", "details": "Détails", "edit": "Modifier", "crm": "CRM", "lead-management": "Gestion des prospects", "landing": "Atterrissage", "title": "Langue Multilingue", "home": "<PERSON><PERSON><PERSON>", "change": "Changer <PERSON>", "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiatnulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollitanim id est laborum.", "account-settings": "Paramètres du compte", "logout": "Se déconnecter", "more-items": "Plus d'articles", "all-users": "Tous les utilisateurs", "user-details": "Détails de l'utilisateur", "back-to-users": "Retour aux utilisateurs", "add-user": "Ajouter un utilisateur", "add-new-user": "Ajouter un nouvel utilisateur", "add-user-description": "Remplissez les détails pour ajouter un nouvel utilisateur au système.", "view": "Voir", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "add": "Ajouter", "save": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nom", "email": "Email", "role": "R<PERSON><PERSON>", "questionnaires": "Questionnaires", "purchases": "Achats", "no-users-found": "Aucun utilisateur trouvé", "first-name": "Prénom", "last-name": "Nom de famille", "username": "Nom d'utilisateur", "phone": "Téléphone", "company": "Entreprise", "first-name-required": "Le prénom est requis", "last-name-required": "Le nom de famille est requis", "username-required": "Le nom d'utilisateur est requis", "valid-email-required": "Une adresse email valide est requise", "user-added-successfully": "Utilisateur ajouté avec succès", "error-adding-user": "<PERSON><PERSON>ur lors de l'ajout de l'utilisateur", "user-updated-successfully": "Utilisateur mis à jour avec succès", "error-updating-user": "Erreur lors de la mise à jour de l'utilisateur: {error}", "user-id-not-provided": "ID utilisateur non fourni", "user-not-found": "Utilisateur non trouvé", "error-fetching-user": "Erreur lors de la récupération de l'utilisateur: {error}", "not-available": "N/A", "invalid-date": "Date invalide", "registration-date": "Date d'inscription", "last-login": "Dernière connexion", "status": "Statut", "active": "Actif", "inactive": "Inactif", "address": "<PERSON><PERSON><PERSON>", "date-created": "Date de création", "recent-activity": "Activité récente", "personal-details": "Détails personnels", "questionnaire-history": "Historique des questionnaires", "purchase-history": "Historique des achats", "no-questionnaires-found": "Aucun questionnaire trouvé", "no-purchases-found": "<PERSON><PERSON><PERSON> achat trouvé", "role-admin": "Administrateur", "role-app-admin": "Administrateur de l'application", "role-doctor": "Médecin", "role-patient": "Patient", "role-client": "Client", "role-unknown": "<PERSON><PERSON><PERSON> inconnu", "title-questionnaire": "Questionnaire", "title-purchase": "<PERSON><PERSON><PERSON>", "created-on": "<PERSON><PERSON><PERSON>", "purchased-on": "<PERSON><PERSON><PERSON> le", "no-description-available": "Aucune description disponible", "price": "Prix", "quantity": "Quantité", "total": "Total", "action": "Action", "save-changes": "Enregistrer les modifications", "discard": "Annuler", "save-success": "Modifications enregistrées avec succès", "save-error": "Erreur lors de l'enregistrement des modifications", "all-clients": "Tous les clients", "add-client": "Ajouter un client", "add-new-client": "Ajouter un nouveau client", "add-client-description": "Remplis<PERSON>z les détails pour ajouter un nouveau client au système.", "no-clients-found": "Aucun client trouvé", "company-name": "Nom de l'entreprise", "company-name-required": "Le nom de l'entreprise est requis", "state": "État/Province", "city": "Ville", "postal-code": "Code postal", "country": "Pays", "notes": "Notes", "actions": "Actions", "confirm-delete": "Confirmer la <PERSON>", "delete-client-confirmation": "Êtes-vous sûr de vouloir supprimer {client}? Cette action ne peut pas être annulée.", "client-added-successfully": "Client ajouté avec succès", "error-adding-client": "<PERSON><PERSON><PERSON> lors de l'ajout du client", "client-deleted-successfully": "Client supprimé avec succès", "error-deleting-client": "<PERSON><PERSON><PERSON> lors de la suppression du client", "all-clinics": "Toutes les cliniques", "add-clinic": "Ajouter une clinique", "add-new-clinic": "Ajouter une nouvelle clinique", "add-clinic-description": "Remplis<PERSON>z les détails pour ajouter une nouvelle clinique au système.", "no-clinics-found": "Aucune clinique trouvée", "clinic-name": "Nom de la clinique", "clinic-name-required": "Le nom de la clinique est requis", "clinic-added-successfully": "Clinique ajoutée avec succès", "error-adding-clinic": "Erreur lors de l'ajout de la clinique", "clinic-deleted-successfully": "Clinique supprimée avec succès", "error-deleting-clinic": "Erreur lors de la suppression de la clinique", "delete-clinic-confirmation": "Êtes-vous sûr de vouloir supprimer {clinic}? Cette action ne peut pas être annulée.", "street": "Rue", "zip-code": "Code postal", "back": "Retour", "error-fetching-clinics": "Erreur lors de la récupération des cliniques: {error}", "clinic-id-not-provided": "ID de clinique non fourni", "clinic-not-found": "Clinique non trouvée", "error-fetching-clinic": "Erreur lors de la récupération de la clinique: {error}", "clinic-updated-successfully": "Clinique mise à jour avec succès", "error-updating-clinic": "Erreur lors de la mise à jour de la clinique: {error}", "back-to-clinics": "Retour aux cliniques", "clinic-details": "Détails de la clinique", "page-not-found": "La page que vous recherchez n'existe pas.", "page-moved-removed": "La page que vous recherchez a peut-être été supprimée, a changé de nom ou est temporairement indisponible.", "support": "Support", "error-404": "Erreur 404", "clients": "Clients", "patient": "Patient", "settings": "Paramètres", "user-management": "Gestion des utilisateurs", "clinics": "Cliniques", "patients": "Patients", "compliance-reports": "Rapports de conformité", "templates": "<PERSON><PERSON><PERSON><PERSON>", "trq-system": "Système TRQ", "client-management": "Gestion des clients", "patient-management": "Gestion des patients", "clinic-management": "Gestion des cliniques", "system-settings": "Paramètres système", "template-management": "Gestion des modèles", "questionnaire-management": "Gestion des questionnaires", "compliance-report-management": "Gestion des rapports de conformité", "create-from-template": "<PERSON><PERSON>er à partir d'un modèle", "create-questionnaire": "<PERSON><PERSON><PERSON> un questionnaire", "assign": "Assigner", "assign-questionnaire": "Assigner un questionnaire", "assigned-on": "<PERSON><PERSON><PERSON>", "button": {"go-back": "Retour", "go-home": "<PERSON>er au tableau de bord"}, "admin-not-found": "Administrateur non trouvé", "error-fetching-admin": "Erreur lors de la récupération de l'administrateur: {error}", "admin-updated-successfully": "Administrateur mis à jour avec succès", "clinic-admin-details": "Détails de l'administrateur de clinique", "no-data-found": "<PERSON><PERSON><PERSON> donn<PERSON> trouvée", "loading-data": "Chargement des données...", "required-field": "Ce champ est requis", "invalid-email": "Adresse e-mail invalide", "success-message": "Opération terminée avec succès", "validation-error": "Erreur de validation", "error.unauthorized.title": "401", "error.unauthorized.heading": "Accès non autorisé", "error.unauthorized.message": "Vous n'avez pas la permission d'accéder à {resource}. Veuillez contacter votre administrateur si vous pensez qu'il s'agit d'une erreur."}