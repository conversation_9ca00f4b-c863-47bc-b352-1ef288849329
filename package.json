{"name": "berry-material-react-ts", "version": "4.0.2", "private": true, "type": "module", "dependencies": {"@auth0/auth0-react": "2.2.4", "@emotion/cache": "11.13.5", "@emotion/react": "11.13.5", "@emotion/styled": "11.13.5", "@fontsource/inter": "5.1.0", "@fontsource/poppins": "5.1.0", "@fontsource/roboto": "5.1.0", "@fullcalendar/core": "6.1.15", "@fullcalendar/daygrid": "6.1.15", "@fullcalendar/interaction": "6.1.15", "@fullcalendar/list": "6.1.15", "@fullcalendar/react": "6.1.15", "@fullcalendar/timegrid": "6.1.15", "@fullcalendar/timeline": "6.1.15", "@hello-pangea/dnd": "17.0.0", "@hookform/resolvers": "3.9.1", "@linear/sdk": "^44.1.0", "@mui/icons-material": "6.1.9", "@mui/lab": "6.0.0-beta.17", "@mui/material": "6.1.9", "@mui/utils": "6.1.9", "@mui/x-data-grid": "7.23.0", "@mui/x-data-grid-generator": "7.23.0", "@mui/x-data-grid-pro": "7.23.0", "@mui/x-date-pickers": "^6.18.0", "@mui/x-tree-view": "7.23.0", "@reduxjs/toolkit": "2.4.0", "@supabase/supabase-js": "2.46.2", "@tabler/icons-react": "3.31.0", "@types/antd": "^1.0.4", "@types/diff": "^7.0.2", "@vitejs/plugin-react": "4.3.4", "amazon-cognito-identity-js": "6.3.12", "antd": "^5.24.7", "apexcharts": "3.46.0", "axios": "1.7.8", "chance": "1.1.12", "currency.js": "2.0.4", "date-fns": "^2.30.0", "diff": "^7.0.0", "draft-js": "0.11.7", "emoji-picker-react": "4.12.0", "firebase": "11.0.2", "formik": "2.4.6", "framer-motion": "11.12.0", "immutable": "^4.3.4", "jwt-decode": "4.0.0", "lodash-es": "4.17.21", "mapbox-gl": "3.1.2", "material-ui-popup-state": "5.3.1", "notistack": "3.0.1", "react": "18.3.1", "react-18-image-lightbox": "5.1.4", "react-apexcharts": "1.4.1", "react-copy-to-clipboard": "5.1.0", "react-csv": "2.2.2", "react-dom": "18.3.1", "react-draft-wysiwyg": "1.15.0", "react-draggable": "4.4.6", "react-dropzone": "14.3.5", "react-fast-marquee": "1.6.5", "react-firebase-hooks": "^5.1.1", "react-google-recaptcha": "3.1.0", "react-hook-form": "7.53.2", "react-intl": "7.0.1", "react-map-gl": "7.1.7", "react-markdown": "9.0.1", "react-number-format": "5.4.2", "react-organizational-chart": "2.2.1", "react-perfect-scrollbar": "1.5.8", "react-quill-new": "3.3.3", "react-redux": "9.1.2", "react-responsive-carousel": "3.2.23", "react-router": "7.0.2", "react-router-dom": "7.0.2", "react-slick": "0.30.2", "react-timer-hook": "3.0.8", "react-to-print": "2.15.1", "react-window": "1.8.10", "react18-input-otp": "1.1.4", "redux": "5.0.1", "redux-persist": "6.0.0", "remark-gfm": "4.0.0", "slick-carousel": "1.8.1", "stylis": "^4.3.1", "stylis-plugin-rtl": "2.1.1", "swr": "2.2.5", "vite": "6.0.2", "vite-tsconfig-paths": "5.1.3", "web-vitals": "4.2.4", "yup": "1.4.0"}, "scripts": {"ui-audit:extract-routes": "tsx scripts/ui-audit/extract-routes.ts", "ui-audit:screenshots": "tsx e2e/scripts/take-screenshots.ts", "ui-audit": "npm run ui-audit:extract-routes && npm run ui-audit:screenshots", "start": "vite", "build": "tsc && vite build", "build-stage": "env-cmd -f .env.qa vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "knip": "knip", "test": "react-scripts test", "test:e2e": "node --require ./e2e/dotenv-setup.cjs ./node_modules/.bin/playwright test --config e2e/playwright.config.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report", "test:e2e:auth-setup": "npx tsx e2e/setup/auth-setup.ts", "e2e:auth": "playwright test e2e/modules/Authentication/", "e2e:layout": "playwright test e2e/modules/Layout/", "e2e:navigation": "playwright test e2e/modules/Navigation/", "e2e:products": "playwright test e2e/modules/Products/", "e2e:questionnaire": "playwright test e2e/modules/Questionnaire/", "test:e2e:nav": "playwright test e2e/modules/Navigation/navigation-rbac.spec.ts", "test:e2e:questionnaire": "playwright test e2e/modules/Questionnaire/admin-questionnaire-assignment.spec.ts", "test:e2e:questionnaire:admin": "playwright test e2e/tests/Questionnaire/Admin/", "test:e2e:questionnaire:admin:assignment": "playwright test e2e/tests/Questionnaire/Admin/admin-questionnaire-assignment.spec.ts", "test:e2e:questionnaire:admin:management": "playwright test e2e/tests/Questionnaire/Admin/admin-questionnaire-management.spec.ts", "test:e2e:questionnaire:patient": "playwright test e2e/tests/Questionnaire/Patient/", "test:e2e:questionnaire:patient:completion": "playwright test e2e/tests/Questionnaire/Patient/patient-questionnaire-completion.spec.ts", "e2e:questionnaires:patient": "playwright test e2e/tests/Questionnaire/Patient/", "test:e2e:products:client": "playwright test e2e/tests/Products/Client/", "test:e2e:products:client:purchase": "playwright test e2e/tests/Products/Client/client-product-purchase-and-assignment.spec.ts", "e2e:products:client": "playwright test e2e/tests/Products/Client/", "test:e2e:patient": "playwright test e2e/features/questionnaires/tests/patient/", "test:e2e:provider": "playwright test e2e/features/questionnaires/tests/provider/", "test:e2e:admin": "playwright test e2e/features/questionnaires/tests/admin/", "test:e2e:single": "cd e2e && node run-individual-test.js", "test:e2e:single:headed": "cd e2e && node run-individual-test.js", "test:e2e:single:debug": "cd e2e && node run-individual-test.js", "test:e2e:single:ui": "cd e2e && node run-individual-test.js", "e2e:populate-db": "npx tsx e2e/scripts/populate-database.ts", "e2e:wipe-db": "npx tsx e2e/scripts/populate-database.ts --clearOnly", "e2e:populate-db-only": "npx tsx e2e/scripts/populate-database.ts --noClear", "e2e:add-standard-auth-users": "npx tsx e2e/scripts/add-standard-auth-users.ts", "e2e:reset-all": "npm run e2e:wipe-auth-users && npm run e2e:wipe-db && npm run e2e:add-standard-auth-users && npm run e2e:populate-db", "e2e:wipe-auth-users": "npx tsx e2e/scripts/wipe-auth-users.ts", "e2e:list-unjoined-users": "npx tsx e2e/scripts/list-unjoined-users.ts", "seed:templates": "tsx scripts/seedTemplates.ts"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version"]}, "devDependencies": {"@eslint/compat": "1.2.3", "@eslint/eslintrc": "3.2.0", "@eslint/js": "^9.24.0", "@playwright/test": "^1.51.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/chance": "1.1.6", "@types/dotenv": "^8.2.3", "@types/jest": "^29.5.14", "@types/lodash-es": "4.17.12", "@types/mapbox-gl": "3.1.0", "@types/node": "22.10.1", "@types/react": "18.3.12", "@types/react-copy-to-clipboard": "5.0.7", "@types/react-countup": "^4.3.1", "@types/react-csv": "1.1.10", "@types/react-dom": "18.3.1", "@types/react-draft-wysiwyg": "1.13.8", "@types/react-google-recaptcha": "2.1.9", "@types/react-slick": "0.23.13", "@types/react-window": "1.8.8", "@types/readline-sync": "^1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "8.17.0", "@typescript-eslint/parser": "8.22.0", "chalk": "^5.3.0", "dotenv": "^16.4.7", "env-cmd": "10.1.0", "eslint": "^9.24.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "5.0.0", "firebase-admin": "^13.2.0", "globals": "^16.0.0", "jest": "^29.7.0", "knip": "5.43.6", "prettier": "3.4.1", "prettier-eslint-cli": "8.0.1", "readline-sync": "^1.4.10", "sass": "1.81.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.7.2", "typescript-eslint": "^8.29.1"}, "packageManager": "yarn@4.7.0"}