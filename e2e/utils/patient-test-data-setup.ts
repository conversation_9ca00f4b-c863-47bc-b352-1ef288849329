import { Page } from '@playwright/test';
import { loginAs } from './auth-utils';
import { Role } from '../../src/RBAC/[types]/Role';
import { AllQuestionnairesPage } from '../pages/Questionnaire/all-questionnaires.page';
import { AssignQuestionnaireDialog } from '../pages/Questionnaire/assign-questionnaire.dialog';
import { TemplateSelectionDialog } from '../pages/Questionnaire/template-selection.dialog';
import { waitForDataGridReady } from './test-data-setup';

export interface TestPatientData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  uid?: string;
}

export interface TestQuestionnaireData {
  id?: string;
  title: string;
  templateName: string;
  patientEmail: string;
}

/**
 * Default test patient data
 */
export const DEFAULT_TEST_PATIENT: TestPatientData = {
  email: '<EMAIL>',
  password: 'password123',
  firstName: 'Test',
  lastName: 'Patient'
};

/**
 * Default respiratory questionnaire data
 */
export const DEFAULT_RESPIRATORY_QUESTIONNAIRE: TestQuestionnaireData = {
  title: 'E2E Test Respiratory Questionnaire',
  templateName: 'general-health', // Use the template ID that exists in tests
  patientEmail: DEFAULT_TEST_PATIENT.email
};

/**
 * Setup test data for patient questionnaire tests
 */
export class PatientTestDataSetup {
  private page: Page;
  private baseURL: string;
  private createdQuestionnaireIds: string[] = [];

  constructor(page: Page, baseURL: string) {
    this.page = page;
    this.baseURL = baseURL;
  }

  /**
   * Create and assign a respiratory questionnaire to a test patient
   */
  async setupRespiratoryQuestionnaireForPatient(
    patientData: TestPatientData = DEFAULT_TEST_PATIENT,
    questionnaireData: TestQuestionnaireData = DEFAULT_RESPIRATORY_QUESTIONNAIRE
  ): Promise<string> {
    console.log('Setting up respiratory questionnaire for patient...');

    // Login as admin to create and assign questionnaire
    await loginAs(this.page, Role.Admin, this.baseURL);

    // Navigate to questionnaires page
    const questionnairesPage = new AllQuestionnairesPage(this.page);
    await questionnairesPage.goto();

    // Wait for page to load
    await this.page.waitForTimeout(2000);

    // Create questionnaire from template
    await questionnairesPage.clickCreateFromTemplate();

    // Select template and create questionnaire
    const templateDialog = new TemplateSelectionDialog(this.page);
    await templateDialog.waitForDialog();
    await templateDialog.selectTemplate(questionnaireData.templateName);
    await templateDialog.submit();

    // Wait for questionnaire creation and dialog to close
    await this.page.waitForTimeout(3000);
    await this.waitForQuestionnaireDataReady();

    // Find the newly created questionnaire (it should be the first one in the list)
    const questionnaireRow = this.page.locator('.MuiDataGrid-row').first();
    await questionnairesPage.clickAssignForRow(questionnaireRow);

    // Assign to patient
    const assignDialog = new AssignQuestionnaireDialog(this.page);
    await assignDialog.selectPatient(patientData.email);
    await assignDialog.assign();

    // Wait for assignment to complete
    await this.page.waitForTimeout(2000);

    // Verify assignment
    await this.page.waitForTimeout(2000);
    await questionnairesPage.verifyQuestionnaireAssigned(questionnaireRow, patientData.firstName);

    console.log(`Successfully created and assigned questionnaire to ${patientData.email}`);

    // Store questionnaire ID for cleanup - try to get it from the row
    const questionnaireId = await this.getQuestionnaireIdFromRow(questionnaireRow);
    if (questionnaireId) {
      this.createdQuestionnaireIds.push(questionnaireId);
    }

    return questionnaireId || '';
  }

  /**
   * Get questionnaire ID from a row element
   */
  private async getQuestionnaireIdFromRow(row: any): Promise<string | null> {
    try {
      // Extract ID from row data attributes
      const rowElement = await row.elementHandle();
      if (rowElement) {
        const dataId = await rowElement.getAttribute('data-id');
        if (dataId) {
          return dataId;
        }
      }

      // Alternative: try to get ID from the row's assign button
      const assignButton = row.locator('button:has-text("Assign")');
      const testId = await assignButton.getAttribute('data-testid');
      if (testId) {
        const match = testId.match(/questionnaire-row-assign-button-(.+)/);
        if (match) {
          return match[1];
        }
      }

      return null;
    } catch (error) {
      console.warn('Could not extract questionnaire ID from row:', error);
      return null;
    }
  }

  /**
   * Verify that a questionnaire is assigned to a patient
   */
  async verifyQuestionnaireAssignment(
    questionnaireTitle: string,
    patientEmail: string
  ): Promise<boolean> {
    try {
      const questionnairesPage = new AllQuestionnairesPage(this.page);
      await questionnairesPage.goto();
      
      const row = questionnairesPage.getQuestionnaireRow(questionnaireTitle);
      await questionnairesPage.verifyQuestionnaireAssigned(row, patientEmail);
      
      return true;
    } catch (error) {
      console.error('Failed to verify questionnaire assignment:', error);
      return false;
    }
  }

  /**
   * Clean up created test data
   */
  async cleanup(): Promise<void> {
    if (this.createdQuestionnaireIds.length === 0) {
      console.log('No test questionnaires to clean up');
      return;
    }

    console.log(`Cleaning up ${this.createdQuestionnaireIds.length} test questionnaires...`);

    try {
      // Login as admin to delete questionnaires
      await loginAs(this.page, Role.Admin, this.baseURL);

      const questionnairesPage = new AllQuestionnairesPage(this.page);
      await questionnairesPage.goto();

      // Delete each created questionnaire
      for (const questionnaireId of this.createdQuestionnaireIds) {
        try {
          await this.deleteQuestionnaireById(questionnaireId);
          await this.page.waitForTimeout(1000);
        } catch (error) {
          console.warn(`Failed to delete questionnaire ${questionnaireId}:`, error);
        }
      }

      console.log('Test data cleanup completed');
    } catch (error) {
      console.error('Failed to clean up test data:', error);
    }

    // Clear the list
    this.createdQuestionnaireIds = [];
  }

  /**
   * Delete a questionnaire by ID
   */
  private async deleteQuestionnaireById(questionnaireId: string): Promise<void> {
    // This would need to be implemented based on the actual UI
    // For now, we'll use a simple approach of finding by title and deleting
    console.log(`Attempting to delete questionnaire ${questionnaireId}`);
    
    // Implementation would depend on how questionnaires are identified in the UI
    // This is a placeholder that would need to be refined based on actual testing
  }

  /**
   * Wait for questionnaire data to be ready
   */
  async waitForQuestionnaireDataReady(): Promise<void> {
    await this.page.waitForTimeout(2000);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * Create multiple test questionnaires for comprehensive testing
   */
  async setupMultipleQuestionnaires(
    patientData: TestPatientData,
    count: number = 3
  ): Promise<string[]> {
    const questionnaireIds: string[] = [];

    for (let i = 0; i < count; i++) {
      const questionnaireData: TestQuestionnaireData = {
        title: `E2E Test Respiratory Questionnaire ${i + 1}`,
        templateName: 'general-health', // Use the template ID that exists in tests
        patientEmail: patientData.email
      };

      const id = await this.setupRespiratoryQuestionnaireForPatient(patientData, questionnaireData);
      if (id) {
        questionnaireIds.push(id);
      }

      // Wait between creations to avoid rate limiting
      await this.page.waitForTimeout(1000);
    }

    return questionnaireIds;
  }

  /**
   * Verify patient can see assigned questionnaires
   */
  async verifyPatientCanSeeQuestionnaires(
    patientData: TestPatientData,
    expectedQuestionnaireCount: number
  ): Promise<boolean> {
    try {
      // Login as patient
      await loginAs(this.page, Role.Patient, this.baseURL);

      // Navigate to patient dashboard
      await this.page.goto('/trq/dashboard');
      await this.page.waitForTimeout(3000);

      // Check for questionnaires in the pending section
      const pendingSection = this.page.locator('text=Pending Questionnaires').locator('..');
      const questionnaireButtons = pendingSection.locator('button:has-text("Start"), button:has-text("Continue")');
      
      const actualCount = await questionnaireButtons.count();
      
      console.log(`Expected ${expectedQuestionnaireCount} questionnaires, found ${actualCount}`);
      
      return actualCount >= expectedQuestionnaireCount;
    } catch (error) {
      console.error('Failed to verify patient questionnaires:', error);
      return false;
    }
  }
}
