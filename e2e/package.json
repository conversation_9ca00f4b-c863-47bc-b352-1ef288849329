{"name": "trq-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for TRQ application", "type": "module", "scripts": {"test": "playwright test", "e2e": "tsx scripts/run-e2e.ts", "e2e:auth": "tsx scripts/run-e2e.ts auth", "e2e:navigation": "tsx scripts/run-e2e.ts navigation", "e2e:products": "tsx scripts/run-e2e.ts products", "e2e:questionnaire": "tsx scripts/run-e2e.ts questionnaire", "e2e:questionnaire:patient": "playwright test tests/Questionnaire/Patient/", "e2e:all": "tsx scripts/run-e2e.ts all", "e2e:setup": "tsx scripts/setup-e2e.ts", "e2e:teardown": "tsx scripts/teardown-e2e.ts", "e2e:full": "tsx scripts/run-e2e.ts all --setup --teardown", "debug:auth": "tsx debug/debug-utils.ts auth", "debug:login": "tsx debug/debug-utils.ts login", "report": "playwright show-report", "setup:database": "tsx scripts/populate-database.ts", "setup:users": "tsx scripts/create-test-users.ts", "setup:auth-states": "tsx scripts/create-auth-states.ts", "setup:all": "tsx scripts/run-all-e2e-setup.ts", "reset:database": "tsx scripts/reset-dev-database.ts", "wipe:auth": "tsx scripts/wipe-auth-users.ts"}, "dependencies": {"dotenv": "^16.0.3", "firebase": "^10.0.0", "playwright": "^1.35.0"}, "devDependencies": {"@playwright/test": "^1.35.0", "tsx": "^3.12.7", "typescript": "^5.0.4"}}