import { test, expect } from '@playwright/test';
import { loginAs } from '../../../utils/auth-utils';
import { Role } from '../../../../src/RBAC/[types]/Role';
import { PatientDashboardPage } from '../../../pages/Patient/patient-dashboard.page';
import { PatientQuestionnaireListPage } from '../../../pages/Patient/patient-questionnaire-list.page';
import { RespiratoryQuestionnaireWizardPage } from '../../../pages/Patient/respiratory-questionnaire-wizard.page';
import { 
  PatientTestDataSetup, 
  DEFAULT_TEST_PATIENT, 
  DEFAULT_RESPIRATORY_QUESTIONNAIRE 
} from '../../../utils/patient-test-data-setup';

test.describe('Patient Questionnaire Completion E2E Tests', () => {
  let patientDashboard: PatientDashboardPage;
  let questionnaireList: PatientQuestionnaireListPage;
  let respiratoryWizard: RespiratoryQuestionnaireWizardPage;
  let testDataSetup: PatientTestDataSetup;
  let baseURL: string;
  let questionnaireId: string;

  test.beforeEach(async ({ page, baseURL: testBaseURL }) => {
    baseURL = testBaseURL || 'http://localhost:3001';
    
    // Initialize page objects
    patientDashboard = new PatientDashboardPage(page);
    questionnaireList = new PatientQuestionnaireListPage(page);
    respiratoryWizard = new RespiratoryQuestionnaireWizardPage(page);
    testDataSetup = new PatientTestDataSetup(page, baseURL);

    console.log('Setting up test data...');
    
    // Setup: Create and assign respiratory questionnaire to test patient
    questionnaireId = await testDataSetup.setupRespiratoryQuestionnaireForPatient(
      DEFAULT_TEST_PATIENT,
      DEFAULT_RESPIRATORY_QUESTIONNAIRE
    );

    expect(questionnaireId).toBeTruthy();
    console.log(`Created questionnaire with ID: ${questionnaireId}`);
  });

  test.afterEach(async () => {
    console.log('Cleaning up test data...');
    await testDataSetup.cleanup();
  });

  test('should complete the full patient questionnaire workflow', async ({ page }) => {
    console.log('Starting patient questionnaire completion test...');

    // Step 1: Patient logs in to their account
    console.log('Step 1: Patient login');
    await loginAs(page, Role.Patient, baseURL);
    
    // Step 2: Patient navigates to their dashboard and sees assigned questionnaires
    console.log('Step 2: Navigate to patient dashboard');
    await patientDashboard.goto();
    await patientDashboard.waitForDashboardToLoad();

    // Verify the questionnaire appears in pending list
    await patientDashboard.verifyPendingQuestionnaireExists(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
    await patientDashboard.verifyQuestionnaireStatus(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title, 'Start');

    // Step 3: Patient opens the respiratory questionnaire
    console.log('Step 3: Open respiratory questionnaire');
    await patientDashboard.clickQuestionnaire(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);

    // Wait for questionnaire wizard to load
    await respiratoryWizard.waitForWizardToLoad();

    // Step 4: Patient completes all required fields in the questionnaire
    console.log('Step 4: Complete questionnaire form');
    
    // Verify we're on step 1
    await respiratoryWizard.verifyCurrentStep(1);

    // Complete the questionnaire with sample data
    await respiratoryWizard.completeQuestionnaireWithSampleData();

    // Step 5: Verify successful submission and appropriate confirmation
    console.log('Step 5: Verify successful submission');
    await respiratoryWizard.verifySuccessfulSubmission();

    // Wait for redirect
    await page.waitForTimeout(3000);

    // Step 6: Verify redirect and questionnaire status update
    console.log('Step 6: Verify completion and status update');
    
    // Should be redirected to questionnaires page or dashboard
    const currentUrl = page.url();
    expect(currentUrl).toMatch(/\/(questionnaires|dashboard|my-questionnaires)/);

    // Navigate back to dashboard to verify status change
    await patientDashboard.goto();
    await patientDashboard.waitForDashboardToLoad();

    // The questionnaire should now appear in completed section or not in pending
    try {
      await patientDashboard.verifyCompletedQuestionnaireExists(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
      console.log('✓ Questionnaire found in completed section');
    } catch (error) {
      // If not in completed section, verify it's no longer in pending
      console.log('Questionnaire not in completed section, checking if removed from pending...');
      const pendingCount = await patientDashboard.getPendingQuestionnaireCount();
      console.log(`Pending questionnaire count: ${pendingCount}`);
    }

    console.log('✅ Patient questionnaire completion workflow test passed!');
  });

  test('should handle questionnaire navigation and form validation', async ({ page }) => {
    console.log('Starting questionnaire navigation and validation test...');

    // Login as patient
    await loginAs(page, Role.Patient, baseURL);
    
    // Navigate to questionnaire
    await patientDashboard.goto();
    await patientDashboard.clickQuestionnaire(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
    await respiratoryWizard.waitForWizardToLoad();

    // Test form validation - try to proceed without filling required fields
    console.log('Testing form validation...');
    
    // Try to click next without accepting terms
    await respiratoryWizard.clickNext();
    
    // Should still be on step 1 due to validation
    await respiratoryWizard.verifyCurrentStep(1);

    // Accept terms and fill minimal required data
    await respiratoryWizard.fillTermsAndEmployeeInfo({
      firstName: 'Test',
      lastName: 'Patient',
      email: '<EMAIL>',
      phone: '555-1234',
      ssn: '***********',
      birthdate: '01/01/1990',
      gender: 'male',
      heightFeet: 6,
      heightInches: 0,
      weight: 180,
      jobTitle: 'Tester',
      bestTimeToCall: '9 AM',
      previouslyWornRespirator: false
    });

    // Now should be able to proceed
    await respiratoryWizard.clickNext();
    await respiratoryWizard.verifyCurrentStep(2);

    // Test back navigation
    await respiratoryWizard.clickBack();
    await respiratoryWizard.verifyCurrentStep(1);

    console.log('✅ Navigation and validation test passed!');
  });

  test('should allow patient to save progress and continue later', async ({ page }) => {
    console.log('Starting save progress test...');

    // Login as patient
    await loginAs(page, Role.Patient, baseURL);
    
    // Start questionnaire
    await patientDashboard.goto();
    await patientDashboard.clickQuestionnaire(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
    await respiratoryWizard.waitForWizardToLoad();

    // Fill first step
    await respiratoryWizard.fillTermsAndEmployeeInfo({
      firstName: 'Test',
      lastName: 'Patient',
      email: '<EMAIL>',
      phone: '555-1234',
      ssn: '***********',
      birthdate: '01/01/1990',
      gender: 'male',
      heightFeet: 6,
      heightInches: 0,
      weight: 180,
      jobTitle: 'Tester',
      bestTimeToCall: '9 AM',
      previouslyWornRespirator: false
    });

    await respiratoryWizard.clickNext();
    await respiratoryWizard.verifyCurrentStep(2);

    // Navigate away (simulating saving progress)
    await patientDashboard.goto();

    // Verify questionnaire now shows "Continue" instead of "Start"
    await patientDashboard.verifyQuestionnaireStatus(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title, 'Continue');

    // Continue the questionnaire
    await patientDashboard.clickQuestionnaire(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
    await respiratoryWizard.waitForWizardToLoad();

    // Should be on step 2 (where we left off)
    await respiratoryWizard.verifyCurrentStep(2);

    console.log('✅ Save progress test passed!');
  });

  test('should display appropriate error messages for invalid data', async ({ page }) => {
    console.log('Starting error handling test...');

    // Login as patient
    await loginAs(page, Role.Patient, baseURL);
    
    // Navigate to questionnaire
    await patientDashboard.goto();
    await patientDashboard.clickQuestionnaire(DEFAULT_RESPIRATORY_QUESTIONNAIRE.title);
    await respiratoryWizard.waitForWizardToLoad();

    // Test invalid email format
    await respiratoryWizard.fillInputByLabel('Email', 'invalid-email');
    await respiratoryWizard.clickNext();

    // Should show validation error
    await respiratoryWizard.verifyValidationError('Please enter a valid email');

    // Test invalid phone format
    await respiratoryWizard.fillInputByLabel('Email', '<EMAIL>');
    await respiratoryWizard.fillInputByLabel('Phone', '123'); // Too short
    await respiratoryWizard.clickNext();

    // Should show validation error for phone
    await respiratoryWizard.verifyValidationError('Please enter a valid phone number');

    console.log('✅ Error handling test passed!');
  });
});
